# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# IOMMU 表生成器项目

## 项目概述
这是一个专门用于生成和优化 IOMMU/VT-d ACPI 表的工具集，主要用于 OpenCore hackintosh 引导和系统研究。项目采用"高保真仿真"策略：使用真实硬件签名和参数，但实施安全的设备管理策略。

## 核心文件

### ivrs.py - AMD IOMMU (IVRS) 表生成器
- **功能**: 从 IVRS.rw 文件提取真实硬件参数，生成定制的 IVRS 表
- **安全策略**: NumberOfDeviceEntries = 0（不管理任何设备）
- **输出**: 生成 `ivrs_custom.asl` 和 `IVRS_raw.aml` 文件
- **使用**: `python ivrs.py IVRS.rw`

### dmar.py - Intel VT-d (DMAR) 表生成器  
- **功能**: 从 DMAR.rw 文件提取真实硬件参数，生成定制的 DMAR 表
- **核心策略**: DRHD Flags=0x01 (INCLUDE_PCI_ALL) 让Windows显示DMA保护开启
- **安全实现**: 无Device Scope条目，实际不管理任何设备
- **特色**: 包含完整 RMRR 结构提取，解决启动卡 logo 问题
- **输出**: 生成 `dmar_custom.asl` 和 `DMAR_raw.aml` 文件  
- **使用**: `python dmar.py DMAR.rw`

## 架构设计

### 高保真仿真策略
1. **真实硬件参数**: 从原始 .rw 文件提取 OEM 签名、寄存器基址等
2. **智能欺骗策略**: 让系统认为保护已启用，但实际不管理设备
3. **完整结构支持**: 保留 RMRR 等关键结构满足硬件启动要求

## 核心命令

### 生成 ACPI 表
```bash
# 生成 AMD IOMMU 表
python ivrs.py IVRS.rw

# 生成 Intel VT-d 表  
python dmar.py DMAR.rw
```

### 调试和验证
```bash
# 反汇编生成的 AML 文件
iasl -d DMAR_raw.aml
iasl -d IVRS_raw.aml

# 编译 ASL 源文件 
iasl dmar_custom.asl
iasl ivrs_custom.asl
```

### 文件组织
```
项目目录/
├── ivrs.py              # AMD IOMMU 生成器
├── dmar.py              # Intel VT-d 生成器  
├── IVRS.rw              # AMD 原始数据输入
├── DMAR.rw              # Intel 原始数据输入
├── *_custom.asl         # 生成的 ASL 源文件
├── *_raw.aml            # 生成的二进制表文件
└── *_raw.dsl            # iasl 反汇编文件
```

## 关键技术点

### IVRS 表结构
- **IV Info**: 从真实硬件提取，保持兼容性
- **IVHD 条目**: Type 11h，使用真实寄存器基址
- **设备条目**: 空，确保 NumberOfDeviceEntries = 0

### DMAR 表结构 - 高级欺骗策略
- **Host Address Width**: 真实值（通常 26 位）
- **DRHD 结构**: 
  - Flags=0x01 (INCLUDE_PCI_ALL) - 让Windows认为管理所有设备
  - 无Device Scope条目 - 实际不管理任何设备
- **RMRR 结构**: 完整提取真实内存保留区域，防止启动问题
- **关键策略**: 声明管理但不实施，既安全又让系统显示保护开启

## 使用场景

### OpenCore 集成
生成的 `*_raw.aml` 文件可直接放入 OpenCore ACPI 目录使用，无需编译。

### 系统测试
生成的表可用于：
- IOMMU 功能测试
- 虚拟化环境调试  
- 硬件兼容性验证

## 故障排除

### 常见问题
1. **编译错误**: 检查 ASL 文件中的 RawDataBuffer 语法
2. **启动卡 logo**: 确保 DMAR 包含必需的 RMRR 结构
3. **DMA保护显示关闭**: 确认 DRHD Flags=0x01 (INCLUDE_PCI_ALL)
4. **设备管理冲突**: 验证无Device Scope条目和 NumberOfDeviceEntries=0

### 调试工具
- `iasl -d *.aml` - 反汇编 AML 文件
- `iasl *.asl` - 编译 ASL 文件
- 查看生成文件的详细信息和校验和

## 技术实现原理

### AMD IOMMU (IVRS) 策略
- 提取真实硬件签名保持系统兼容性
- NumberOfDeviceEntries = 0 确保不管理任何设备
- 生成最小化但有效的 IVHD 结构

### Intel VT-d (DMAR) 高级策略  
- **双重安全机制**:
  1. DRHD Flags=0x01 声明"管理所有PCI设备" (让Windows显示保护开启)
  2. 无Device Scope条目实际不管理任何设备 (保持系统安全)
- **RMRR完整保留**: 提取真实内存保留区域避免启动冲突
- **校验和自动计算**: 确保生成的表通过ACPI验证

## 安全声明
本工具仅用于合法的系统研究和 hackintosh 引导优化。采用"智能欺骗"策略：让系统认为IOMMU保护已启用，但实际不对硬件进行任何管理操作。