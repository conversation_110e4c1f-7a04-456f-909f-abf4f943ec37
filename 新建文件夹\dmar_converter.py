#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMAR.rw 到 DMAR.asl 自动转换器
用于OpenCore VT-d欺骗表生成
"""

import re
import sys
import os
from datetime import datetime

def parse_dmar_rw(file_path):
    """解析DMAR.rw文件，提取关键信息"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"错误：读取文件失败 - {e}")
        return None
    
    dmar_info = {}
    extracted_count = 0
    
    # 定义所有需要提取的参数
    extraction_patterns = {
        'length': r'Length\s+0x([0-9A-Fa-f]+)',
        'revision': r'Revision\s+0x([0-9A-Fa-f]+)',
        'oem_id': r'OEM ID\s+"([^"]+)"',
        'oem_table_id': r'OEM Table ID\s+"([^"]+)"',
        'oem_revision': r'OEM Revision\s+0x([0-9A-Fa-f]+)',
        'creator_id': r'Creator ID\s+"([^"]*)"',
        'creator_revision': r'Creator Revision\s+0x([0-9A-Fa-f]+)',
        'host_width': r'Host Address Width\s+0x([0-9A-Fa-f]+)',
        'register_base': r'Register Base Address\s+0x([0-9A-Fa-f]+)'
    }
    
    # 提取所有参数
    for key, pattern in extraction_patterns.items():
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            value = match.group(1).strip()
            if key in ['oem_id', 'creator_id']:
                # 字符串参数，进行长度调整
                target_len = 6 if key == 'oem_id' else 4
                dmar_info[key] = value.ljust(target_len)[:target_len]
            elif key == 'oem_table_id':
                # OEM Table ID 8字节
                dmar_info[key] = value.ljust(8)[:8]
            else:
                # 数值参数
                dmar_info[key] = int(value, 16)
            extracted_count += 1
    
    # 验证关键参数
    if 'register_base' not in dmar_info:
        print("警告：未找到寄存器基址，将使用默认值")
    
    print(f"成功提取 {extracted_count}/{len(extraction_patterns)} 个参数")
    return dmar_info

def generate_asl_content(dmar_info):
    """根据解析的信息生成ASL内容"""
    
    # 默认值
    defaults = {
        'revision': 1,
        'oem_id': 'INTEL ',
        'oem_table_id': 'EDK2    ',
        'oem_revision': 2,
        'creator_id': '    ',
        'creator_revision': 0x01000013,
        'host_width': 0x26,
        'register_base': 0xFED91000
    }
    
    # 合并默认值和解析值
    info = {**defaults, **dmar_info}
    
    # 转换字符串为字节数组
    def str_to_bytes(s, length):
        return [ord(c) for c in s.ljust(length)[:length]]
    
    oem_id_bytes = str_to_bytes(info['oem_id'], 6)
    oem_table_bytes = str_to_bytes(info['oem_table_id'], 8)
    creator_id_bytes = str_to_bytes(info['creator_id'], 4)
    
    # 转换数值为字节数组（小端序）
    def int_to_bytes(value, length):
        return [(value >> (8 * i)) & 0xFF for i in range(length)]
    
    oem_rev_bytes = int_to_bytes(info['oem_revision'], 4)
    creator_rev_bytes = int_to_bytes(info['creator_revision'], 4)
    reg_base_bytes = int_to_bytes(info['register_base'], 8)
    
    # 确定使用的是真实值还是默认值
    using_real_oem = 'oem_id' in dmar_info
    using_real_reg = 'register_base' in dmar_info
    
    asl_content = f'''/*
 * 自动生成的DMAR表 - 用于OpenCore VT-d欺骗
 * 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
 * 硬件签名: {info['oem_id'].strip()} / {info['oem_table_id'].strip()} {'(真实)' if using_real_oem else '(默认)'}
 * 寄存器基址: 0x{info['register_base']:08X} {'(真实)' if using_real_reg else '(默认)'}
 * 主机地址宽度: {info['host_width']}位
 * 核心策略: Flags=0x00 不管理任何设备，确保安全启动
 */

DefinitionBlock ("DMAR.aml", "DMAR", {info['revision']}, "{info['oem_id']}", "{info['oem_table_id']}", 0x{info['oem_revision']:08X})
{{
    Name (DMAR, Buffer (0x40)  // 64字节 - 最小DMAR表
    {{
        /* ACPI表头 (36字节) */
        0x44, 0x4D, 0x41, 0x52,  // "DMAR"
        0x40, 0x00, 0x00, 0x00,  // 长度64字节
        0x{info['revision']:02X},                    // 版本
        0x00,                    // 校验和(自动计算)
        
        /* OEM信息 */
        {', '.join(f'0x{b:02X}' for b in oem_id_bytes)},  // OEM ID "{info['oem_id']}"
        {', '.join(f'0x{b:02X}' for b in oem_table_bytes)},  // OEM Table ID "{info['oem_table_id']}"
        {', '.join(f'0x{b:02X}' for b in oem_rev_bytes)},  // OEM版本
        {', '.join(f'0x{b:02X}' for b in creator_id_bytes)},  // 创建者ID "{info['creator_id']}"
        {', '.join(f'0x{b:02X}' for b in creator_rev_bytes)},  // 创建者版本
        
        /* DMAR特定头 (12字节) */
        0x{info['host_width']:02X},                    // 主机地址宽度({info['host_width']}位)
        0x01,                    // 标志(中断重映射)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 保留
        
        /* DRHD结构 - 关键欺骗部分 (16字节) */
        0x00, 0x00,              // 类型(DRHD)
        0x10, 0x00,              // 长度16字节
        0x00,                    // 标志0x00=不包含PCI设备(关键!)
        0x00,                    // 保留
        0x00, 0x00,              // 段号
        {', '.join(f'0x{b:02X}' for b in reg_base_bytes)}  // 寄存器基址0x{info['register_base']:08X}
    }})
}}'''
    
    return asl_content

def main():
    """主函数"""
    print("DMAR.rw 到 DMAR.asl 自动转换器 v1.1")
    print("=" * 45)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', '/?']:
            print("用法:")
            print(f"  python {sys.argv[0]} [输入文件] [输出文件]")
            print(f"  python {sys.argv[0]} -b [目录]  # 批量转换")
            print()
            print("示例:")
            print(f"  python {sys.argv[0]} DMAR.rw DMAR.asl")
            print(f"  python {sys.argv[0]} -b ./dumps/  # 转换dumps目录下所有.rw文件")
            print()
            print("默认:")
            print(f"  python {sys.argv[0]}  # 自动查找当前目录的dmar.rw -> DMAR.asl")
            return 0
        
        if sys.argv[1] == '-b':
            # 批量转换模式
            directory = sys.argv[2] if len(sys.argv) > 2 else '.'
            return batch_convert(directory)
        
        input_file = sys.argv[1]
    else:
        # 自动查找当前目录的dmar.rw文件
        input_file = find_dmar_rw_file()
        if input_file is None:
            return 1
    
    if len(sys.argv) > 2 and sys.argv[1] != '-b':
        output_file = sys.argv[2]
    else:
        output_file = "DMAR.asl"
    
    return convert_single_file(input_file, output_file)

def find_dmar_rw_file():
    """在当前目录查找dmar.rw文件（不区分大小写）"""
    current_dir = '.'
    possible_names = ['dmar.rw', 'DMAR.rw', 'Dmar.rw', 'dmar.RW', 'DMAR.RW']
    
    for filename in possible_names:
        filepath = os.path.join(current_dir, filename)
        if os.path.exists(filepath):
            print(f"找到输入文件: {filename}")
            return filepath
    
    print("错误：未在当前目录找到 dmar.rw 文件")
    print("请确保以下文件之一存在：dmar.rw, DMAR.rw")
    return None

def convert_single_file(input_file, output_file):
    """转换单个文件"""
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        print("使用 -h 参数查看帮助")
        return 1
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print()
    
    # 解析DMAR.rw文件
    print("正在解析DMAR.rw文件...")
    dmar_info = parse_dmar_rw(input_file)
    
    if dmar_info is None:
        return 1
    
    # 显示解析结果
    print("解析结果:")
    for key, value in dmar_info.items():
        if isinstance(value, int):
            print(f"  {key}: 0x{value:X}")
        else:
            print(f"  {key}: {value}")
    print()
    
    # 生成ASL内容
    print("正在生成ASL文件...")
    asl_content = generate_asl_content(dmar_info)
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(asl_content)
        
        # 验证文件生成
        file_size = os.path.getsize(output_file)
        print(f"成功生成: {output_file} ({file_size} 字节)")
        
        # 快速验证ASL内容
        if validate_asl_content(asl_content):
            print("ASL文件验证通过")
        else:
            print("警告：ASL文件可能存在问题")
        
        print()
        print("使用说明:")
        print("1. 编译: iasl DMAR.asl")
        print("2. 将生成的 DMAR.aml 放入OpenCore的ACPI文件夹")
        print("3. 在config.plist中添加到ACPI->Add部分")
        print("4. 重启后系统将认为VT-d已启用，DMA保护显示开启")
        
    except Exception as e:
        print(f"错误：写入文件失败 - {e}")
        return 1

def validate_asl_content(asl_content):
    """快速验证ASL内容的基本正确性"""
    checks = [
        'DefinitionBlock' in asl_content,
        'Name (DMAR, Buffer' in asl_content,
        '0x44, 0x4D, 0x41, 0x52' in asl_content,  # "DMAR" signature
        '0x40, 0x00, 0x00, 0x00' in asl_content,  # 64 bytes length
        '0x00,' in asl_content and 'DRHD' in asl_content  # DRHD flags
    ]
    return all(checks)
    
    return 0

def batch_convert(directory):
    """批量转换目录下的所有.rw文件"""
    print(f"批量转换模式 - 目录: {directory}")
    print()
    
    if not os.path.exists(directory):
        print(f"错误：目录不存在 {directory}")
        return 1
    
    # 查找所有.rw文件
    rw_files = []
    for file in os.listdir(directory):
        if file.lower().endswith('.rw'):
            rw_files.append(file)
    
    if not rw_files:
        print("未找到任何.rw文件")
        return 1
    
    print(f"找到 {len(rw_files)} 个.rw文件:")
    for file in rw_files:
        print(f"  - {file}")
    print()
    
    success_count = 0
    for rw_file in rw_files:
        input_path = os.path.join(directory, rw_file)
        output_path = os.path.join(directory, rw_file.replace('.rw', '.asl'))
        
        print(f"转换: {rw_file} -> {os.path.basename(output_path)}")
        
        if convert_single_file(input_path, output_path) == 0:
            success_count += 1
        print("-" * 40)
    
    print(f"批量转换完成: {success_count}/{len(rw_files)} 个文件成功")
    return 0 if success_count == len(rw_files) else 1

if __name__ == "__main__":
    sys.exit(main())