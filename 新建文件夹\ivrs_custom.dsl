/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembling to symbolic ASL+ operators
 *
 * Disassembly of ivrs_custom.aml
 *
 * Original Table Header:
 *     Signature        "SSDT"
 *     Length           0x0000007E (126)
 *     Revision         0x02
 *     Checksum         0x84
 *     OEM ID           "AMD   "
 *     OEM Table ID     "IVRSTBL"
 *     OEM Revision     0x00000001 (1)
 *     Compiler ID      "INTL"
 *     Compiler Version 0x20250404 (539296772)
 */
DefinitionBlock ("", "SSDT", 2, "AMD   ", "IVRSTBL", 0x00000001)
{
    Name (IVRS, Buffer (0x54)
    {
        /* 0000 */  0x49, 0x56, 0x52, 0x53, 0x54, 0x00, 0x00, 0x00,  // IVRST...
        /* 0008 */  0x02, 0x00, 0x41, 0x4D, 0x44, 0x20, 0x20, 0x20,  // ..AMD   
        /* 0010 */  0x41, 0x6D, 0x64, 0x54, 0x61, 0x62, 0x6C, 0x65,  // AmdTable
        /* 0018 */  0x01, 0x00, 0x00, 0x00, 0x41, 0x4D, 0x44, 0x20,  // ....AMD 
        /* 0020 */  0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // ........
        /* 0028 */  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // ........
        /* 0030 */  0x11, 0x00, 0x24, 0x00, 0x00, 0x00, 0x18, 0x00,  // ..$.....
        /* 0038 */  0x00, 0x00, 0xD8, 0xFE, 0x00, 0x00, 0x00, 0x00,  // ........
        /* 0040 */  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // ........
        /* 0048 */  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00   // ........
    })
}

