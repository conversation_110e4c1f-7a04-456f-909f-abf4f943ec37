/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembly of IVRS_Phantom.aml
 *
 * ACPI Data Table [IVRS]
 *
 * Format: [HexOffset DecimalOffset ByteLength]  FieldName : FieldValue (in hex)
 */

[000h 0000 004h]                   Signature : "IVRS"    [I/O Virtualization Reporting Structure]
[004h 0004 004h]                Table Length : 00000048
[008h 0008 001h]                    Revision : 02
[009h 0009 001h]                    Checksum : 0B
[00Ah 0010 006h]                      Oem ID : "AMD   "
[010h 0016 008h]                Oem Table ID : "AmdTable"
[018h 0024 004h]                Oem Revision : 00000001
[01Ch 0028 004h]             Asl Compiler ID : "AMD "
[020h 0032 004h]       Asl Compiler Revision : 00000001

[024h 0036 004h]         Virtualization Info : 00000303
[028h 0040 008h]                    Reserved : 0000000000000000

[030h 0048 001h]               Subtable Type : 11 [Hardware Definition Block (IVHD)]
[031h 0049 001h]       Flags (decoded below) : 00
                                     HtTunEn : 0
                                      PassPW : 0
                                   ResPassPW : 0
                                Isoc Control : 0
                               Iotlb Support : 0
                                    Coherent : 0
                            Prefetch Support : 0
                                 PPR Support : 0
[032h 0050 002h]                      Length : 0018
[034h 0052 002h]                    DeviceId : 0000
[036h 0054 002h]           Capability Offset : 0018
[038h 0056 008h]                Base Address : 0000000000000000
[040h 0064 002h]           PCI Segment Group : 0000
[042h 0066 002h]         Virtualization Info : 0000
[044h 0068 004h]                  Attributes : 00000000
/**** ACPI table terminates in the middle of a data structure! (dump table)
CurrentOffset: 48, TableLength: 48 ***/
Raw Table Data: Length 72 (0x48)

    0000: 49 56 52 53 48 00 00 00 02 0B 41 4D 44 20 20 20  // IVRSH.....AMD   
    0010: 41 6D 64 54 61 62 6C 65 01 00 00 00 41 4D 44 20  // AmdTable....AMD 
    0020: 01 00 00 00 03 03 00 00 00 00 00 00 00 00 00 00  // ................
    0030: 11 00 18 00 00 00 18 00 00 00 00 00 00 00 00 00  // ................
    0040: 00 00 00 00 00 00 00 00                          // ........
