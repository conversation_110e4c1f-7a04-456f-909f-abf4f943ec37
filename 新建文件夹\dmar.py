#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DMAR ASL Generator - 从DMAR.rw真实数据生成定制的DMAR表
核心策略：伪装VT-d已启用，但使用Flags=0x00确保不卡开机logo
基于真实硬件数据，使用安全的"零设备管理"策略
用于OpenCore VT-d欺骗和兼容性测试
"""

import re
import sys
import os
from datetime import datetime
from pathlib import Path

# ACPI表结构常量
ACPI_HEADER_SIZE = 36
DMAR_HEADER_SIZE = 12
DRHD_HEADER_SIZE = 16

class DMARGenerator:
    """
    解析DMAR.rw文件并生成一个安全的、用于VT-d欺骗的DMAR表。
    核心功能：
    1. 提取真实硬件签名信息以提高兼容性
    2. 提取真实VT-d寄存器基地址和参数
    3. 生成"零设备管理"的DMAR表（Flags=0x00）
    4. 确保系统认为VT-d已启用但不会卡开机logo
    """
    def __init__(self, rw_file_path):
        self.rw_file_path = Path(rw_file_path)
        if not self.rw_file_path.exists():
            raise FileNotFoundError(f"Error: Input file not found at {rw_file_path}")

        # 初始化默认值，确保不会出现None值错误
        self.dmar_data = {
            'oem_id': "INTEL",
            'oem_table_id': "EDK2",
            'oem_revision': 2,
            'creator_id': "    ",
            'creator_revision': 0x01000013,
            'revision': 1,
            'host_address_width': 0x26,  # 39 bits
            'primary_register_base': 0xFED91000,  # 默认VT-d寄存器基址
            'primary_flags': 0x00,       # 核心：确保不卡开机logo
            'primary_segment': 0x0000,   # PCI段0
            'drhd_units': [],
            'rmrr_units': []
        }

    def parse_rw_file(self):
        """解析DMAR.rw文件，提取关键硬件签名信息和VT-d参数。"""
        try:
            content = self.rw_file_path.read_text(encoding='utf-8', errors='replace')
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试二进制模式读取
            content = self.rw_file_path.read_bytes().decode('latin1')

        print(f"正在解析文件: {self.rw_file_path}")

        # 基本表头信息提取
        basic_patterns = {
            'oem_id': r'OEM ID\s+"([^"]+)"',
            'oem_table_id': r'OEM Table ID\s+"([^"]+)"',
            'oem_revision': r'OEM Revision\s+0x([0-9A-F]+)',
            'creator_id': r'Creator ID\s+"([^"]*)"',
            'creator_revision': r'Creator Revision\s+0x([0-9A-F]+)',
            'length': r'Length\s+0x([0-9A-F]+)',
            'revision': r'Revision\s+0x([0-9A-F]+)',
            'host_address_width': r'Host Address Width\s+0x([0-9A-F]+)',
        }

        extracted_count = 0
        for key, pattern in basic_patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if 'id' in key:
                    self.dmar_data[key] = value
                else:
                    self.dmar_data[key] = int(value, 16)
                extracted_count += 1

        print(f"✓ 成功提取 {extracted_count}/{len(basic_patterns)} 个基本参数")

        # 从原始数据中提取真实VT-d硬件参数
        self._extract_real_dmar_params(content)

        # 显示最终使用的参数
        self._display_final_params()

    def _extract_real_dmar_params(self, content):
        """从DMAR.rw原始数据中提取真实的VT-d硬件参数"""
        print("正在提取VT-d硬件参数...")

        try:
            # 提取寄存器基地址
            register_bases = []
            for line in content.split('\n'):
                reg_base_match = re.search(r'Register Base Address\s+0x([0-9A-F]+)', line, re.IGNORECASE)
                if reg_base_match:
                    register_base = int(reg_base_match.group(1), 16)
                    register_bases.append(register_base)
                    print(f"✓ 发现VT-d寄存器基地址: 0x{register_base:016X}")

            if register_bases:
                # 使用第一个发现的寄存器基地址
                self.dmar_data['primary_register_base'] = register_bases[0]
                self.dmar_data['register_bases'] = register_bases

                # 提取DRHD信息（但强制使用安全策略）
                self._extract_drhd_info_from_text(content)

                print(f"✓ 采集到真实VT-d参数:")
                print(f"  - 寄存器基址: 0x{register_bases[0]:016X} (真实)")
                print(f"  - 强制标志: 0x00 (安全策略，确保不卡开机logo)")
                print(f"  - 段号: 0x0000 (安全策略)")
                print(f"[核心策略] Flags=0x00 确保VT-d欺骗成功且不影响启动")
            else:
                print("[WARN] 未找到寄存器基地址，使用默认值")

        except Exception as e:
            print(f"[WARN] 解析VT-d参数时出错: {e}")
            print("[INFO] 使用默认VT-d参数")
    
    def _extract_drhd_info_from_text(self, content):
        """从文本中提取DRHD结构信息，但强制使用安全策略"""
        drhd_units = []
        current_drhd = None

        for line in content.split('\n'):
            line = line.strip()

            # 检测DRHD结构开始
            if 'DMA Remapping Hardware Unit Definition Structure' in line:
                if current_drhd:
                    drhd_units.append(current_drhd)
                current_drhd = {'type': 0x0000, 'flags': 0x00, 'segment': 0x0000}
                continue

            if current_drhd is not None:
                # 提取长度
                length_match = re.search(r'Length\s+0x([0-9A-F]+)', line, re.IGNORECASE)
                if length_match:
                    current_drhd['length'] = int(length_match.group(1), 16)

                # 提取原始标志（但不使用）
                flags_match = re.search(r'Flags\s+0x([0-9A-F]+)', line, re.IGNORECASE)
                if flags_match:
                    current_drhd['original_flags'] = int(flags_match.group(1), 16)

                # 提取段号
                segment_match = re.search(r'Segment Number\s+0x([0-9A-F]+)', line, re.IGNORECASE)
                if segment_match:
                    current_drhd['original_segment'] = int(segment_match.group(1), 16)

                # 提取寄存器基地址
                reg_base_match = re.search(r'Register Base Address\s+0x([0-9A-F]+)', line, re.IGNORECASE)
                if reg_base_match:
                    current_drhd['register_base'] = int(reg_base_match.group(1), 16)

        # 添加最后一个DRHD
        if current_drhd:
            drhd_units.append(current_drhd)

        if drhd_units:
            # 使用第一个有效的DRHD单元，但强制安全策略
            for drhd in drhd_units:
                if 'register_base' in drhd:
                    self.dmar_data['primary_register_base'] = drhd['register_base']
                    # >>> 核心策略：强制Flags=0x00，确保VT-d欺骗成功且不卡开机logo <<<
                    self.dmar_data['primary_flags'] = 0x00  # 安全策略，不管理任何设备
                    self.dmar_data['primary_segment'] = 0x0000  # 强制安全段号
                    self.dmar_data['original_flags'] = drhd.get('original_flags', 0x01)
                    self.dmar_data['original_segment'] = drhd.get('original_segment', 0x0000)

                    print(f"✓ 真实DRHD参数分析:")
                    print(f"  - 原始标志: 0x{drhd.get('original_flags', 0):02X} -> 强制使用: 0x00")
                    print(f"  - 原始段号: 0x{drhd.get('original_segment', 0):04X} -> 强制使用: 0x0000")
                    print(f"  - 寄存器基址: 0x{drhd['register_base']:016X} (保持真实值)")
                    print(f"  - DRHD长度: {drhd.get('length', 16)} 字节")
                    break

            self.dmar_data['drhd_units'] = drhd_units
        else:
            print("[WARN] 未找到有效的DRHD结构，使用默认参数")
    
    def _display_final_params(self):
        """显示最终使用的DMAR参数"""
        print("\n=== 最终使用的DMAR参数 ===")
        print(f"OEM ID: '{self.dmar_data['oem_id']}'")
        print(f"OEM Table ID: '{self.dmar_data['oem_table_id']}'")
        print(f"OEM Revision: 0x{int(self.dmar_data['oem_revision']):08X}")
        creator_id = self.dmar_data['creator_id'] or '    '  # 确保不为空
        print(f"Creator ID: '{creator_id}'")
        print(f"Creator Revision: 0x{int(self.dmar_data['creator_revision']):08X}")
        print(f"Host Address Width: {int(self.dmar_data['host_address_width'])} bits (0x{int(self.dmar_data['host_address_width']):02X})")
        print(f"VT-d Register Base: 0x{int(self.dmar_data['primary_register_base']):016X}")
        print(f"DRHD Flags: 0x{int(self.dmar_data['primary_flags']):02X} (强制安全策略)")
        print(f"PCI Segment: 0x{int(self.dmar_data['primary_segment']):04X}")

        # 显示策略说明
        print("\n=== VT-d 欺骗策略 ===")
        print("✓ 使用真实硬件签名提高兼容性")
        print("✓ 使用真实VT-d寄存器基地址")
        print("✓ 强制Flags=0x00确保不卡开机logo")
        print("✓ 系统检测：VT-d存在但不管理设备")
        print("✓ 结果：满足VT-d检测要求，确保安全启动")
        print("=" * 35)
    


    def generate_asl(self):
        """根据提取的数据和安全策略，生成ASL文件内容。
        核心策略：使用真实硬件参数但确保Flags=0x00
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 安全地获取参数，确保类型正确
        oem_id = str(self.dmar_data.get('oem_id', 'INTEL')).ljust(6)[:6]
        oem_table_id = str(self.dmar_data.get('oem_table_id', 'EDK2')).ljust(8)[:8]
        oem_revision = int(self.dmar_data.get('oem_revision', 2))
        creator_id = str(self.dmar_data.get('creator_id', '    ')).ljust(4)[:4]
        creator_revision = int(self.dmar_data.get('creator_revision', 0x01000013))
        revision = int(self.dmar_data.get('revision', 1))
        host_width = int(self.dmar_data.get('host_address_width', 0x26))

        # 获取真实的VT-d参数
        register_base = int(self.dmar_data.get('primary_register_base', 0xFED91000))
        drhd_flags = int(self.dmar_data.get('primary_flags', 0x00))  # 核心：确保为0x00
        pci_segment = int(self.dmar_data.get('primary_segment', 0x0000))

        # 计算表长度 (ACPI头 + DMAR特殊头 + 最小DRHD结构)
        # 核心：DRHD条目只包含头部，没有设备条目 = 不管理任何设备
        dmar_header_len = DMAR_HEADER_SIZE  # 12字节
        drhd_entry_len = DRHD_HEADER_SIZE   # 16字节（只有头部，无设备条目）
        total_data_len = dmar_header_len + drhd_entry_len
        total_table_len = ACPI_HEADER_SIZE + total_data_len  # 36 + 12 + 16 = 64字节

        # 判断是否使用了真实数据
        using_real_data = (
            self.dmar_data.get('oem_id') != 'INTEL' or
            self.dmar_data.get('primary_register_base') != 0xFED91000
        )

        # 计算表长度 (ACPI头 + DMAR特殊头 + 最小DRHD结构)
        # 核心：DRHD条目只包含头部，没有设备条目 = 不管理任何设备
        dmar_header_len = DMAR_HEADER_SIZE  # 12字节
        drhd_entry_len = DRHD_HEADER_SIZE   # 16字节（只有头部，无设备条目）
        total_data_len = dmar_header_len + drhd_entry_len
        total_table_len = ACPI_HEADER_SIZE + total_data_len  # 36 + 12 + 16 = 64字节
        
        asl_content = f'''/*
 * 自动生成的DMAR表 - 用于OpenCore VT-d欺骗
 * 生成时间: {timestamp}
 * 硬件签名: {oem_id.strip()} / {oem_table_id.strip()} {'(真实数据)' if using_real_data else '(默认数据)'}
 * VT-d寄存器基址: 0x{register_base:016X}
 * Host Address Width: {host_width} bits
 * 核心策略: Flags=0x00 不管理任何设备，确保安全启动
 *
 * 重要说明：
 * - 此表伪装VT-d已启用，满足系统检测要求
 * - Flags=0x00确保不会卡在开机logo
 * - 使用真实硬件参数提高兼容性
 */

DefinitionBlock ("", "SSDT", 2, "{oem_id}", "DMRTBL", 0x00000001)
{{
    /*
     * DMAR Raw Data Table - ACPI compliant structure
     * Total length: {total_table_len} bytes (0x{total_table_len:02X})
     * Creator ID: "{creator_id}" (0x{creator_revision:08X})
     *
     * 核心策略说明：
     * 1. 使用真实硬件参数提高兼容性
     * 2. DRHD条目长度={drhd_entry_len}字节，只包含头部
     * 3. Flags=0x00，确保不卡开机logo
     * 4. 系统检测到VT-d存在但不管理任何设备
     */
    Name (DMAR, Buffer (0x{total_table_len:02X})
    {{
        /* ACPI Standard Header (36 bytes) */
        0x44, 0x4D, 0x41, 0x52,                         // Signature: "DMAR"
        0x{total_table_len & 0xFF:02X}, 0x{(total_table_len >> 8) & 0xFF:02X}, 0x{(total_table_len >> 16) & 0xFF:02X}, 0x{(total_table_len >> 24) & 0xFF:02X}, // Length: {total_table_len} bytes
        0x{revision:02X},                               // Revision: {revision}
        0x00,                                           // Checksum (calculated by BIOS)
        0x{ord(oem_id[0]):02X}, 0x{ord(oem_id[1]):02X}, 0x{ord(oem_id[2]):02X}, 0x{ord(oem_id[3]):02X}, 0x{ord(oem_id[4]):02X}, 0x{ord(oem_id[5]):02X}, // OEM ID: "{oem_id}"
        0x{ord(oem_table_id[0]):02X}, 0x{ord(oem_table_id[1]):02X}, 0x{ord(oem_table_id[2]):02X}, 0x{ord(oem_table_id[3]):02X}, 0x{ord(oem_table_id[4]):02X}, 0x{ord(oem_table_id[5]):02X}, 0x{ord(oem_table_id[6]):02X}, 0x{ord(oem_table_id[7]):02X}, // OEM Table ID: "{oem_table_id}"
        0x{oem_revision & 0xFF:02X}, 0x{(oem_revision >> 8) & 0xFF:02X}, 0x{(oem_revision >> 16) & 0xFF:02X}, 0x{(oem_revision >> 24) & 0xFF:02X}, // OEM Revision: 0x{oem_revision:08X}
        0x{ord(creator_id[0]):02X}, 0x{ord(creator_id[1]):02X}, 0x{ord(creator_id[2]):02X}, 0x{ord(creator_id[3]):02X}, // Creator ID: "{creator_id}"
        0x{creator_revision & 0xFF:02X}, 0x{(creator_revision >> 8) & 0xFF:02X}, 0x{(creator_revision >> 16) & 0xFF:02X}, 0x{(creator_revision >> 24) & 0xFF:02X}, // Creator Revision: 0x{creator_revision:08X}

        /* DMAR-specific Header (12 bytes) - 使用真实参数 */
        0x{host_width:02X},                             // Host Address Width: {host_width} bits (真实)
        0x01,                                           // Flags: Interrupt Remapping (真实)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Reserved fields

        /* DRHD Entry - 使用真实硬件参数但确保Flags=0x00 */
        0x00, 0x00,                                     // Type: DRHD (DMA Remapping Hardware Unit Definition)
        0x{drhd_entry_len & 0xFF:02X}, 0x{(drhd_entry_len >> 8) & 0xFF:02X}, // Length: {drhd_entry_len} bytes (只有头部)
        0x{drhd_flags:02X},                             // Flags: 0x{drhd_flags:02X} (核心：确保不卡开机logo)
        0x00,                                           // Reserved
        0x{pci_segment & 0xFF:02X}, 0x{(pci_segment >> 8) & 0xFF:02X}, // Segment Number: 0x{pci_segment:04X} (真实)
        {', '.join(f'0x{b:02X}' for b in register_base.to_bytes(8, 'little'))} // Register Base Address: 0x{register_base:016X} (真实)

        /* >>> 核心VT-d欺骗策略 <<< */
        /* 此DRHD条目长度={drhd_entry_len}字节，只包含16字节头部 */
        /* 没有任何设备范围条目跟随 = 不管理任何设备 */
        /* 系统检测：VT-d存在且已启用，但不保护任何设备 */
        /* 结果：满足VT-d检测要求，同时确保不卡开机logo */
    }})
}}'''
        return asl_content

    def generate_raw_dmar(self):
        """生成简化的DMAR二进制数据 - 核心策略：Flags=0x00确保不卡开机logo"""
        # 安全地获取参数，确保类型正确
        oem_id = str(self.dmar_data.get('oem_id', 'INTEL')).ljust(6)[:6].encode('ascii')
        oem_table_id = str(self.dmar_data.get('oem_table_id', 'EDK2')).ljust(8)[:8].encode('ascii')
        oem_revision = int(self.dmar_data.get('oem_revision', 2))
        creator_id = str(self.dmar_data.get('creator_id', '    ')).ljust(4)[:4].encode('ascii')
        creator_revision = int(self.dmar_data.get('creator_revision', 0x01000013))
        revision = int(self.dmar_data.get('revision', 1))
        host_width = int(self.dmar_data.get('host_address_width', 0x26))
        register_base = int(self.dmar_data.get('primary_register_base', 0xFED91000))

        # 计算表长度：ACPI头(36) + DMAR头(12) + DRHD条目(16) = 64字节
        total_table_len = ACPI_HEADER_SIZE + DMAR_HEADER_SIZE + DRHD_HEADER_SIZE

        print(f"✓ 生成DMAR表长度: {total_table_len} 字节")
        print(f"✓ 使用真实VT-d寄存器基址: 0x{register_base:016X}")
        print(f"✓ 核心策略: Flags=0x00 确保不卡开机logo")

        # 构建DMAR表数据
        dmar_data = bytearray()

        # ACPI标准头 (36 bytes)
        dmar_data.extend(b'DMAR')                                    # Signature
        dmar_data.extend(total_table_len.to_bytes(4, 'little'))      # Length
        dmar_data.append(revision)                                   # Revision
        dmar_data.append(0x00)                                       # Checksum (will be calculated)
        dmar_data.extend(oem_id)                                     # OEM ID (真实)
        dmar_data.extend(oem_table_id)                               # OEM Table ID (真实)
        dmar_data.extend(oem_revision.to_bytes(4, 'little'))         # OEM Revision (真实)
        dmar_data.extend(creator_id)                                 # Creator ID (真实)
        dmar_data.extend(creator_revision.to_bytes(4, 'little'))     # Creator Revision (真实)

        # DMAR特殊头 (12 bytes) - 使用真实参数
        dmar_data.append(host_width)                                 # Host Address Width (真实)
        dmar_data.append(0x01)                                       # Flags: Interrupt Remapping (真实)
        dmar_data.extend(b'\x00' * 10)                               # Reserved

        # DRHD条目 - 核心策略：使用真实硬件参数但Flags=0x00 (16 bytes)
        dmar_data.extend(b'\x00\x00')                                # Type: DRHD
        dmar_data.extend(DRHD_HEADER_SIZE.to_bytes(2, 'little'))     # Length: 16
        dmar_data.append(0x00)                                       # Flags: 0x00 = 核心策略，不管理任何设备
        dmar_data.append(0x00)                                       # Reserved
        dmar_data.extend(b'\x00\x00')                                # Segment Number: 0x0000
        dmar_data.extend(register_base.to_bytes(8, 'little'))        # Register Base (真实)

        # 计算校验和
        checksum = (256 - (sum(dmar_data) % 256)) % 256
        dmar_data[9] = checksum

        return bytes(dmar_data)
    


    def save_asl(self, output_path=None):
        """保存生成的ASL文件"""
        if output_path is None:
            output_path = self.rw_file_path.parent / "dmar_custom.asl"
        elif isinstance(output_path, str):
            output_path = Path(output_path)

        asl_content = self.generate_asl()
        output_path.write_text(asl_content, encoding='utf-8')
        print(f"✓ 成功生成ASL文件: {output_path}")
        print("✓ 结合真实硬件签名与安全VT-d策略")

    def save_raw_dmar(self, output_path=None):
        """保存纯DMAR二进制文件"""
        if output_path is None:
            output_path = self.rw_file_path.parent / "DMAR_raw.aml"
        elif isinstance(output_path, str):
            output_path = Path(output_path)

        dmar_data = self.generate_raw_dmar()
        output_path.write_bytes(dmar_data)
        print(f"✓ 成功生成DMAR二进制文件: {output_path}")
        print(f"✓ 文件大小: {len(dmar_data)} 字节，零设备映射策略")

def find_dmar_rw_file():
    """在当前目录查找dmar.rw文件（不区分大小写）"""
    current_dir = '.'
    possible_names = ['dmar.rw', 'DMAR.rw', 'Dmar.rw', 'dmar.RW', 'DMAR.RW']

    for filename in possible_names:
        filepath = os.path.join(current_dir, filename)
        if os.path.exists(filepath):
            print(f"找到输入文件: {filename}")
            return filepath

    print("错误：未在当前目录找到 dmar.rw 文件")
    print("请确保以下文件之一存在：dmar.rw, DMAR.rw")
    return None

def convert_single_file(input_file, output_file):
    """转换单个文件"""
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        return 1

    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print()

    try:
        generator = DMARGenerator(input_file)
        generator.parse_rw_file()

        # 生成ASL文件
        print("正在生成ASL文件...")
        generator.save_asl(output_file)

        # 验证文件生成
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"成功生成: {output_file} ({file_size} 字节)")
            print("ASL文件验证通过")

        print()
        print("使用说明:")
        print("1. 编译: iasl DMAR.asl")
        print("2. 将生成的 DMAR.aml 放入OpenCore的ACPI文件夹")
        print("3. 在config.plist中添加到ACPI->Add部分")
        print("4. 重启后系统将认为VT-d已启用")

        return 0
    except Exception as e:
        print(f"错误：处理失败 - {e}")
        return 1

def main():
    """主函数"""
    print("DMAR.rw 到 DMAR.asl 自动转换器 v1.1")
    print("=" * 45)

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', '/?']:
            print("用法:")
            print(f"  python {sys.argv[0]} [输入文件] [输出文件]")
            print()
            print("示例:")
            print(f"  python {sys.argv[0]} DMAR.rw DMAR.asl")
            print()
            print("默认:")
            print(f"  python {sys.argv[0]}  # 自动查找当前目录的dmar.rw -> DMAR.asl")
            return 0

        input_file = sys.argv[1]
    else:
        # 自动查找当前目录的dmar.rw文件
        input_file = find_dmar_rw_file()
        if input_file is None:
            return 1

    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    else:
        output_file = "DMAR.asl"

    return convert_single_file(input_file, output_file)

if __name__ == "__main__":
    main()