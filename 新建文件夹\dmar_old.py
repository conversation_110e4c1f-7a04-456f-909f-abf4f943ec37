#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMAR.rw 到 DMAR.asl 自动转换器
用于OpenCore VT-d欺骗表生成
"""

import re
import sys
import os
from datetime import datetime

def parse_dmar_rw(file_path):
    """解析DMAR.rw文件，提取关键信息"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"错误：读取文件失败 - {e}")
        return None
    
    dmar_info = {}
    
    # 提取基本信息
    length_match = re.search(r'Length\s+0x([0-9A-Fa-f]+)', content)
    if length_match:
        dmar_info['length'] = int(length_match.group(1), 16)
    
    revision_match = re.search(r'Revision\s+0x([0-9A-Fa-f]+)', content)
    if revision_match:
        dmar_info['revision'] = int(revision_match.group(1), 16)
    
    # 提取OEM信息
    oem_id_match = re.search(r'OEM ID\s+"([^"]+)"', content)
    if oem_id_match:
        dmar_info['oem_id'] = oem_id_match.group(1).ljust(6)[:6]
    
    oem_table_match = re.search(r'OEM Table ID\s+"([^"]+)"', content)
    if oem_table_match:
        dmar_info['oem_table_id'] = oem_table_match.group(1).ljust(8)[:8]
    
    oem_revision_match = re.search(r'OEM Revision\s+0x([0-9A-Fa-f]+)', content)
    if oem_revision_match:
        dmar_info['oem_revision'] = int(oem_revision_match.group(1), 16)
    
    # 提取Creator信息
    creator_id_match = re.search(r'Creator ID\s+"([^"]*)"', content)
    if creator_id_match:
        dmar_info['creator_id'] = creator_id_match.group(1).ljust(4)[:4]
    
    creator_revision_match = re.search(r'Creator Revision\s+0x([0-9A-Fa-f]+)', content)
    if creator_revision_match:
        dmar_info['creator_revision'] = int(creator_revision_match.group(1), 16)
    
    # 提取Host Address Width
    host_width_match = re.search(r'Host Address Width\s+0x([0-9A-Fa-f]+)', content)
    if host_width_match:
        dmar_info['host_width'] = int(host_width_match.group(1), 16)
    
    # 提取Register Base Address
    reg_base_match = re.search(r'Register Base Address\s+0x([0-9A-Fa-f]+)', content)
    if reg_base_match:
        dmar_info['register_base'] = int(reg_base_match.group(1), 16)
    
    return dmar_info

def generate_binary_dmar(dmar_info):
    """根据解析的信息生成二进制DMAR数据"""
    
    # 默认值
    defaults = {
        'revision': 1,
        'oem_id': 'INTEL ',
        'oem_table_id': 'EDK2    ',
        'oem_revision': 2,
        'creator_id': '    ',
        'creator_revision': 0x01000013,
        'host_width': 0x26,
        'register_base': 0xFED91000
    }
    
    # 合并默认值和解析值
    info = {**defaults, **dmar_info}
    
    # 构建二进制DMAR数据
    dmar_data = bytearray()
    
    # ACPI表头 (36字节)
    dmar_data.extend(b'DMAR')                                        # Signature
    dmar_data.extend((64).to_bytes(4, 'little'))                     # Length: 64字节
    dmar_data.append(info['revision'])                               # Revision
    dmar_data.append(0)                                              # Checksum (计算后填入)
    
    # OEM信息
    oem_id = info['oem_id'].ljust(6)[:6].encode('ascii')
    dmar_data.extend(oem_id)
    
    oem_table_id = info['oem_table_id'].ljust(8)[:8].encode('ascii')
    dmar_data.extend(oem_table_id)
    
    dmar_data.extend(info['oem_revision'].to_bytes(4, 'little'))
    
    creator_id = info['creator_id'].ljust(4)[:4].encode('ascii')
    dmar_data.extend(creator_id)
    
    dmar_data.extend(info['creator_revision'].to_bytes(4, 'little'))
    
    # DMAR特定头 (12字节)
    dmar_data.append(info['host_width'])                             # Host Address Width
    dmar_data.append(0x01)                                           # Flags: 中断重映射
    dmar_data.extend(b'\x00' * 10)                                   # Reserved
    
    # DRHD条目 (16字节) - 与dmar_old.py完全一致的关键欺骗结构
    dmar_data.extend(b'\x00\x00')                                    # Type: DRHD
    dmar_data.extend((16).to_bytes(2, 'little'))                     # Length: 16字节
    dmar_data.append(0x00)                                           # Flags: 0x00 = 不包含PCI设备(关键!)
    dmar_data.append(0x00)                                           # Reserved
    dmar_data.extend(b'\x00\x00')                                    # Segment Number
    dmar_data.extend(info['register_base'].to_bytes(8, 'little'))    # Register Base
    
    # 计算校验和
    checksum = (256 - (sum(dmar_data) % 256)) % 256
    dmar_data[9] = checksum
    
    return bytes(dmar_data)

def generate_asl_content(dmar_info):
    """根据解析的信息生成ASL内容"""
    
    # 默认值
    defaults = {
        'revision': 1,
        'oem_id': 'INTEL ',
        'oem_table_id': 'EDK2    ',
        'oem_revision': 2,
        'creator_id': '    ',
        'creator_revision': 0x01000013,
        'host_width': 0x26,
        'register_base': 0xFED91000
    }
    
    # 合并默认值和解析值
    info = {**defaults, **dmar_info}
    
    # 转换字符串为字节数组
    def str_to_bytes(s, length):
        return [ord(c) for c in s.ljust(length)[:length]]
    
    oem_id_bytes = str_to_bytes(info['oem_id'], 6)
    oem_table_bytes = str_to_bytes(info['oem_table_id'], 8)
    creator_id_bytes = str_to_bytes(info['creator_id'], 4)
    
    # 转换数值为字节数组（小端序）
    def int_to_bytes(value, length):
        return [(value >> (8 * i)) & 0xFF for i in range(length)]
    
    oem_rev_bytes = int_to_bytes(info['oem_revision'], 4)
    creator_rev_bytes = int_to_bytes(info['creator_revision'], 4)
    reg_base_bytes = int_to_bytes(info['register_base'], 8)
    
    asl_content = f'''/*
 * 自动生成的DMAR表 - 用于OpenCore VT-d欺骗
 * 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
 * 目标: 让系统认为VT-d已启用，但实际不管理任何设备
 * Register Base: 0x{info['register_base']:08X}
 * 关键: Flags = 0x00 确保不包含任何PCI设备
 */

DefinitionBlock ("DMAR.aml", "DMAR", {info['revision']}, "{info['oem_id']}", "{info['oem_table_id']}", 0x{info['oem_revision']:08X})
{{
    Name (DMAR, Buffer (0x40)  // 64字节 - 最小DMAR表
    {{
        /* ACPI表头 (36字节) */
        0x44, 0x4D, 0x41, 0x52,  // "DMAR"
        0x40, 0x00, 0x00, 0x00,  // 长度64字节
        0x{info['revision']:02X},                    // 版本
        0x00,                    // 校验和(自动计算)
        
        /* OEM信息 */
        {', '.join(f'0x{b:02X}' for b in oem_id_bytes)},  // OEM ID "{info['oem_id']}"
        {', '.join(f'0x{b:02X}' for b in oem_table_bytes)},  // OEM Table ID "{info['oem_table_id']}"
        {', '.join(f'0x{b:02X}' for b in oem_rev_bytes)},  // OEM版本
        {', '.join(f'0x{b:02X}' for b in creator_id_bytes)},  // 创建者ID "{info['creator_id']}"
        {', '.join(f'0x{b:02X}' for b in creator_rev_bytes)},  // 创建者版本
        
        /* DMAR特定头 (12字节) */
        0x{info['host_width']:02X},                    // 主机地址宽度({info['host_width']}位)
        0x01,                    // 标志(中断重映射)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 保留
        
        /* DRHD结构 - 关键欺骗部分 (16字节) */
        0x00, 0x00,              // 类型(DRHD)
        0x10, 0x00,              // 长度16字节
        0x00,                    // 标志0x00=不包含PCI设备(关键!)
        0x00,                    // 保留
        0x00, 0x00,              // 段号
        {', '.join(f'0x{b:02X}' for b in reg_base_bytes)}  // 寄存器基址0x{info['register_base']:08X}
    }})
}}'''
    
    return asl_content

def main():
    """主函数"""
    print("DMAR.rw 到 DMAR.asl 自动转换器 v1.0")
    print("=" * 45)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', '/?']:
            print("用法:")
            print(f"  python {sys.argv[0]} [输入文件] [输出文件]")
            print(f"  python {sys.argv[0]} -b [目录]  # 批量转换")
            print()
            print("示例:")
            print(f"  python {sys.argv[0]} DMAR.rw DMAR.asl")
            print(f"  python {sys.argv[0]} -b ./dumps/  # 转换dumps目录下所有.rw文件")
            print()
            print("默认:")
            print(f"  python {sys.argv[0]}  # 等同于 DMAR.rw -> DMAR.asl")
            return 0
        
        if sys.argv[1] == '-b':
            # 批量转换模式
            directory = sys.argv[2] if len(sys.argv) > 2 else '.'
            return batch_convert(directory)
        
        input_file = sys.argv[1]
    else:
        input_file = "DMAR.rw"
    
    if len(sys.argv) > 2 and sys.argv[1] != '-b':
        output_file = sys.argv[2]
    else:
        output_file = "DMAR.asl"
    
    return convert_single_file(input_file, output_file)

def convert_single_file(input_file, output_file):
    """转换单个文件"""
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        print("使用 -h 参数查看帮助")
        return 1
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print()
    
    # 解析DMAR.rw文件
    print("正在解析DMAR.rw文件...")
    dmar_info = parse_dmar_rw(input_file)
    
    if dmar_info is None:
        return 1
    
    # 显示解析结果
    print("解析结果:")
    for key, value in dmar_info.items():
        if isinstance(value, int):
            print(f"  {key}: 0x{value:X}")
        else:
            print(f"  {key}: {value}")
    print()
    
    # 生成ASL内容
    print("正在生成ASL文件...")
    asl_content = generate_asl_content(dmar_info)
    
    # 生成二进制DMAR数据
    print("正在生成AML二进制文件...")
    binary_data = generate_binary_dmar(dmar_info)
    
    # 写入ASL文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(asl_content)
        print(f"成功生成ASL文件: {output_file}")
        
        # 写入AML二进制文件
        aml_file = output_file.replace('.asl', '_raw.aml')
        with open(aml_file, 'wb') as f:
            f.write(binary_data)
        print(f"成功生成AML文件: {aml_file}")
        print(f"  - 文件大小: {len(binary_data)} 字节")
        print()
        print("使用说明:")
        print("1. 直接使用: 将生成的 *_raw.aml 文件放入OpenCore的ACPI文件夹")
        print("2. 或者编译: 使用 iasl 编译.asl文件后使用编译产生的.aml")
        print("3. 在config.plist中添加到ACPI->Add部分")
        print("4. 重启后系统将认为VT-d已启用，DMA保护显示开启")
        
    except Exception as e:
        print(f"错误：写入文件失败 - {e}")
        return 1
    
    return 0

def batch_convert(directory):
    """批量转换目录下的所有.rw文件"""
    print(f"批量转换模式 - 目录: {directory}")
    print()
    
    if not os.path.exists(directory):
        print(f"错误：目录不存在 {directory}")
        return 1
    
    # 查找所有.rw文件
    rw_files = []
    for file in os.listdir(directory):
        if file.lower().endswith('.rw'):
            rw_files.append(file)
    
    if not rw_files:
        print("未找到任何.rw文件")
        return 1
    
    print(f"找到 {len(rw_files)} 个.rw文件:")
    for file in rw_files:
        print(f"  - {file}")
    print()
    
    success_count = 0
    for rw_file in rw_files:
        input_path = os.path.join(directory, rw_file)
        output_path = os.path.join(directory, rw_file.replace('.rw', '.asl'))
        
        print(f"转换: {rw_file} -> {os.path.basename(output_path)}")
        
        if convert_single_file(input_path, output_path) == 0:
            success_count += 1
        print("-" * 40)
    
    print(f"批量转换完成: {success_count}/{len(rw_files)} 个文件成功")
    return 0 if success_count == len(rw_files) else 1

if __name__ == "__main__":
    sys.exit(main())