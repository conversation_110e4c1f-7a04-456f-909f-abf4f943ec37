#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IVRS Phantom IOMMU Generator (DAT版本) - "幻影IOMMU"生成器
从IVRS.dat二进制文件生成一个"存在但不可用"的IVRS表。
核心策略：IOMMU Base Address = 0
"""

import struct
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# ACPI表结构常量
ACPI_HEADER_SIZE = 36
IVRS_HEADER_SIZE = 12

class IVRSEntry:
    """IVRS设备条目基类"""
    def __init__(self, entry_type: int, device_id: int, data_setting: int):
        self.entry_type = entry_type
        self.device_id = device_id
        self.data_setting = data_setting

    def to_bytes(self) -> bytes:
        """转换为二进制格式"""
        return struct.pack('<BHB', self.entry_type, self.device_id, self.data_setting)

class IVRSAliasEntry(IVRSEntry):
    """别名设备条目"""
    def __init__(self, entry_type: int, device_id: int, data_setting: int, 
                 source_device_id: int):
        super().__init__(entry_type, device_id, data_setting)
        self.source_device_id = source_device_id

    def to_bytes(self) -> bytes:
        return struct.pack('<BHBBHB', self.entry_type, self.device_id, 
                          self.data_setting, 0, self.source_device_id, 0)

class IVRSSpecialEntry(IVRSEntry):
    """特殊设备条目"""
    def __init__(self, entry_type: int, device_id: int, data_setting: int,
                 handle: int, source_device_id: int, variety: int):
        super().__init__(entry_type, device_id, data_setting)
        self.handle = handle
        self.source_device_id = source_device_id
        self.variety = variety

    def to_bytes(self) -> bytes:
        return struct.pack('<BHBBHB', self.entry_type, self.device_id,
                          self.data_setting, self.handle, self.source_device_id,
                          self.variety)

class IVHDBlock:
    """IVHD (I/O Virtualization Hardware Definition) 块"""
    def __init__(self, ivhd_type: int, flags: int, device_id: int,
                 cap_offset: int, base_addr: int, pci_seg: int,
                 iommu_info: int, attributes: Optional[int] = None,
                 efr_image: Optional[int] = None):
        self.type = ivhd_type
        self.flags = flags
        self.device_id = device_id
        self.cap_offset = cap_offset
        self.base_addr = base_addr
        self.pci_seg = pci_seg
        self.iommu_info = iommu_info
        self.attributes = attributes
        self.efr_image = efr_image
        self.entries: List[IVRSEntry] = []

    def add_entry(self, entry: IVRSEntry):
        """添加设备条目"""
        self.entries.append(entry)

    def to_bytes(self) -> bytes:
        """转换为二进制格式"""
        # 计算条目数据
        entries_data = b''.join(entry.to_bytes() for entry in self.entries)
        
        if self.type == 0x10:
            # Type 10h: 标准IVHD
            header_size = 24
            total_length = header_size + len(entries_data)
            header = struct.pack('<BBHHHHQHHI',
                               self.type, self.flags, total_length,
                               self.device_id, self.cap_offset, 0,  # Reserved
                               self.base_addr, self.pci_seg, self.iommu_info,
                               0)  # Feature Reporting (仅Type 10h有)
        else:
            # Type 11h: 增强型IVHD
            header_size = 40
            total_length = header_size + len(entries_data)
            header = struct.pack('<BBHHHHQHHIQQ',
                               self.type, self.flags, total_length,
                               self.device_id, self.cap_offset, 0,  # Reserved
                               self.base_addr, self.pci_seg, self.iommu_info,
                               self.attributes or 0, self.efr_image or 0, 0)
        
        return header + entries_data

class IVRSPhantomGeneratorDAT:
    def __init__(self, dat_file_path: str):
        self.dat_file_path = Path(dat_file_path)
        if not self.dat_file_path.exists():
            raise FileNotFoundError(f"错误: 找不到输入文件 {dat_file_path}")
        
        self.acpi_header = {}
        self.ivrs_header = {}
        self.ivhd_blocks: List[IVHDBlock] = []

    def parse_dat_file(self):
        """解析IVRS.dat二进制文件"""
        data = self.dat_file_path.read_bytes()
        print(f"正在解析文件: {self.dat_file_path}")
        print(f"文件大小: {len(data)} 字节")
        
        # 1. 解析ACPI标准头 (36字节)
        self._parse_acpi_header(data)
        
        # 2. 解析IVRS特定头 (12字节)
        self._parse_ivrs_header(data)
        
        # 3. 解析IVHD块和设备条目
        self._parse_ivhd_blocks(data)
        
        # 4. 显示解析结果
        self._display_parsed_info()

    def _parse_acpi_header(self, data: bytes):
        """解析ACPI标准头部"""
        header = struct.unpack_from('<4sIBB6s8sI4sI', data, 0)
        self.acpi_header = {
            'signature': header[0],
            'length': header[1],
            'revision': header[2],
            'checksum': header[3],
            'oem_id': header[4].decode('ascii').rstrip(),
            'oem_table_id': header[5].decode('ascii').rstrip(),
            'oem_revision': header[6],
            'creator_id': header[7].decode('ascii').rstrip(),
            'creator_revision': header[8]
        }

    def _parse_ivrs_header(self, data: bytes):
        """解析IVRS特定头部"""
        iv_info = struct.unpack_from('<I8x', data, ACPI_HEADER_SIZE)[0]
        self.ivrs_header = {
            'iv_info': iv_info
        }

    def _parse_ivhd_blocks(self, data: bytes):
        """解析所有IVHD块"""
        offset = ACPI_HEADER_SIZE + IVRS_HEADER_SIZE
        
        while offset < len(data):
            # 读取IVHD类型和长度
            if offset + 4 > len(data):
                break
                
            ivhd_type = data[offset]
            length = struct.unpack_from('<H', data, offset + 2)[0]
            
            if ivhd_type in [0x10, 0x11]:
                ivhd = self._parse_single_ivhd(data, offset, ivhd_type)
                if ivhd:
                    self.ivhd_blocks.append(ivhd)
                offset += length
            else:
                break

    def _parse_single_ivhd(self, data: bytes, offset: int, ivhd_type: int) -> Optional[IVHDBlock]:
        """解析单个IVHD块"""
        if ivhd_type == 0x10:
            # Type 10h IVHD
            header = struct.unpack_from('<BBHHHHQHHI', data, offset)
            ivhd = IVHDBlock(
                ivhd_type=header[0],
                flags=header[1],
                device_id=header[3],
                cap_offset=header[4],
                base_addr=header[6],
                pci_seg=header[7],
                iommu_info=header[8]
            )
            entries_offset = offset + 24
            entries_end = offset + header[2]
            
        elif ivhd_type == 0x11:
            # Type 11h IVHD (增强型)
            header = struct.unpack_from('<BBHHHHQHHIQQ', data, offset)
            ivhd = IVHDBlock(
                ivhd_type=header[0],
                flags=header[1],
                device_id=header[3],
                cap_offset=header[4],
                base_addr=header[6],
                pci_seg=header[7],
                iommu_info=header[8],
                attributes=header[9],
                efr_image=header[10]
            )
            entries_offset = offset + 40
            entries_end = offset + header[2]
        else:
            return None
        
        # 解析设备条目
        self._parse_device_entries(data, entries_offset, entries_end, ivhd)
        return ivhd

    def _parse_device_entries(self, data: bytes, start: int, end: int, ivhd: IVHDBlock):
        """解析设备条目"""
        offset = start
        while offset < end and offset < len(data):
            entry_type = data[offset]
            
            if entry_type in [0x03, 0x04]:  # Start/End of Range
                if offset + 4 <= len(data):
                    device_id = struct.unpack_from('<H', data, offset + 1)[0]
                    data_setting = data[offset + 3]
                    ivhd.add_entry(IVRSEntry(entry_type, device_id, data_setting))
                    offset += 4
                else:
                    break
                    
            elif entry_type == 0x43:  # Alias Start of Range
                if offset + 8 <= len(data):
                    device_id = struct.unpack_from('<H', data, offset + 1)[0]
                    data_setting = data[offset + 3]
                    source_id = struct.unpack_from('<H', data, offset + 5)[0]
                    ivhd.add_entry(IVRSAliasEntry(entry_type, device_id, data_setting, source_id))
                    offset += 8
                else:
                    break
                    
            elif entry_type == 0x48:  # Special Device
                if offset + 8 <= len(data):
                    device_id = struct.unpack_from('<H', data, offset + 1)[0]
                    data_setting = data[offset + 3]
                    handle = data[offset + 4]
                    source_id = struct.unpack_from('<H', data, offset + 5)[0]
                    variety = data[offset + 7]
                    ivhd.add_entry(IVRSSpecialEntry(entry_type, device_id, data_setting,
                                                   handle, source_id, variety))
                    offset += 8
                else:
                    break
            else:
                # 未知条目类型，跳过
                offset += 1

    def _display_parsed_info(self):
        """显示解析的信息"""
        print("\n=== 解析的IVRS表信息 ===")
        print(f"OEM ID: '{self.acpi_header['oem_id']}'")
        print(f"OEM Table ID: '{self.acpi_header['oem_table_id']}'")
        print(f"OEM Revision: 0x{self.acpi_header['oem_revision']:08X}")
        print(f"IV Info: 0x{self.ivrs_header['iv_info']:08X}")
        print(f"IVHD块数量: {len(self.ivhd_blocks)}")
        
        for i, ivhd in enumerate(self.ivhd_blocks):
            print(f"\nIVHD块 #{i+1}:")
            print(f"  类型: 0x{ivhd.type:02X}")
            print(f"  标志: 0x{ivhd.flags:02X}")
            print(f"  设备ID: 0x{ivhd.device_id:04X}")
            print(f"  基地址: 0x{ivhd.base_addr:016X}")
            print(f"  设备条目数: {len(ivhd.entries)}")

    def generate_phantom_ivrs(self, strategy: str = "minimal") -> bytes:
        """
        生成幻影IVRS表
        
        策略选项:
        - "minimal": 最小化表，只包含一个IVHD块
        - "preserve": 保留原始结构，只修改基地址
        """
        print(f"\n[策略] 应用'{strategy}'幻影IOMMU策略...")
        
        if strategy == "minimal":
            return self._generate_minimal_phantom()
        else:
            return self._generate_preserved_phantom()

    def _generate_minimal_phantom(self) -> bytes:
        """生成最小化的幻影IVRS表"""
        print("[生成] 创建最小化IVRS表...")
        
        # 使用第一个IVHD块的参数（如果存在）
        if self.ivhd_blocks:
            ref_ivhd = self.ivhd_blocks[0]
            ivhd_type = 0x11  # 使用增强型
            ivhd_flags = ref_ivhd.flags & 0xF0  # 保留主要标志
            device_id = ref_ivhd.device_id
            cap_offset = ref_ivhd.cap_offset
            pci_seg = ref_ivhd.pci_seg
        else:
            # 默认值
            ivhd_type = 0x11
            ivhd_flags = 0x30
            device_id = 0x0002
            cap_offset = 0x0040
            pci_seg = 0x0000
        
        # 创建单个IVHD块，基地址为0
        ivhd = IVHDBlock(
            ivhd_type=ivhd_type,
            flags=ivhd_flags,
            device_id=device_id,
            cap_offset=cap_offset,
            base_addr=0,  # 核心：基地址为0
            pci_seg=pci_seg,
            iommu_info=0,
            attributes=0,
            efr_image=0
        )
        
        # 添加最基本的设备范围
        ivhd.add_entry(IVRSEntry(0x03, 0x0000, 0x00))  # Start of range
        ivhd.add_entry(IVRSEntry(0x04, 0xFFFF, 0x00))  # End of range
        
        # 构建完整表
        ivhd_data = ivhd.to_bytes()
        table_length = ACPI_HEADER_SIZE + IVRS_HEADER_SIZE + len(ivhd_data)
        
        # 构建二进制数据
        ivrs_data = bytearray(table_length)
        
        # ACPI头
        struct.pack_into('<4sIBB6s8sI4sI', ivrs_data, 0,
                        b'IVRS', table_length, 2, 0,
                        self.acpi_header['oem_id'].ljust(6).encode('ascii')[:6],
                        self.acpi_header['oem_table_id'].ljust(8).encode('ascii')[:8],
                        self.acpi_header['oem_revision'],
                        self.acpi_header['creator_id'].ljust(4).encode('ascii')[:4],
                        self.acpi_header['creator_revision'])
        
        # IVRS头
        struct.pack_into('<I8x', ivrs_data, ACPI_HEADER_SIZE, 
                        self.ivrs_header['iv_info'])
        
        # IVHD数据
        ivrs_data[ACPI_HEADER_SIZE + IVRS_HEADER_SIZE:] = ivhd_data
        
        # 计算校验和
        checksum = (256 - (sum(ivrs_data) % 256)) % 256
        ivrs_data[9] = checksum
        
        print(f"[完成] 生成表大小: {len(ivrs_data)} 字节")
        return bytes(ivrs_data)

    def _generate_preserved_phantom(self) -> bytes:
        """生成保留原始结构的幻影IVRS表"""
        print("[生成] 保留原始结构，仅修改基地址...")
        
        # 修改所有IVHD块的基地址为0
        phantom_blocks = []
        for ivhd in self.ivhd_blocks:
            print(f"[修改] IVHD基地址: 0x{ivhd.base_addr:016X} -> 0x0000000000000000")
            
            # 创建修改后的IVHD块
            phantom_ivhd = IVHDBlock(
                ivhd_type=ivhd.type,
                flags=ivhd.flags,
                device_id=ivhd.device_id,
                cap_offset=ivhd.cap_offset,
                base_addr=0,  # 核心修改
                pci_seg=ivhd.pci_seg,
                iommu_info=ivhd.iommu_info,
                attributes=ivhd.attributes,
                efr_image=ivhd.efr_image
            )
            
            # 复制所有设备条目
            for entry in ivhd.entries:
                phantom_ivhd.add_entry(entry)
            
            phantom_blocks.append(phantom_ivhd)
        
        # 构建完整表
        ivhd_data = b''.join(block.to_bytes() for block in phantom_blocks)
        table_length = ACPI_HEADER_SIZE + IVRS_HEADER_SIZE + len(ivhd_data)
        
        # 构建二进制数据
        ivrs_data = bytearray(table_length)
        
        # ACPI头
        struct.pack_into('<4sIBB6s8sI4sI', ivrs_data, 0,
                        b'IVRS', table_length,
                        self.acpi_header['revision'],
                        0,  # 校验和稍后计算
                        self.acpi_header['oem_id'].ljust(6).encode('ascii')[:6],
                        self.acpi_header['oem_table_id'].ljust(8).encode('ascii')[:8],
                        self.acpi_header['oem_revision'],
                        self.acpi_header['creator_id'].ljust(4).encode('ascii')[:4],
                        self.acpi_header['creator_revision'])
        
        # IVRS头
        struct.pack_into('<I8x', ivrs_data, ACPI_HEADER_SIZE,
                        self.ivrs_header['iv_info'])
        
        # IVHD数据
        ivrs_data[ACPI_HEADER_SIZE + IVRS_HEADER_SIZE:] = ivhd_data
        
        # 计算校验和
        checksum = (256 - (sum(ivrs_data) % 256)) % 256
        ivrs_data[9] = checksum
        
        print(f"[完成] 生成表大小: {len(ivrs_data)} 字节")
        return bytes(ivrs_data)

    def save_phantom_ivrs(self, output_path: Optional[str] = None, 
                         strategy: str = "minimal"):
        """保存幻影IVRS文件"""
        if output_path is None:
            suffix = "_minimal" if strategy == "minimal" else "_preserved"
            output_path = self.dat_file_path.parent / f"IVRS_Phantom{suffix}.aml"
        else:
            output_path = Path(output_path)
        
        ivrs_data = self.generate_phantom_ivrs(strategy)
        output_path.write_bytes(ivrs_data)
        
        print(f"\n✓ 成功生成幻影IVRS文件: {output_path}")
        print(f"✓ 文件大小: {len(ivrs_data)} 字节")
        print(f"✓ 策略: {strategy}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(f"用法: python {sys.argv[0]} <IVRS.dat路径> [策略]")
        print("策略选项:")
        print("  minimal   - 最小化表结构（默认）")
        print("  preserve  - 保留原始结构")
        sys.exit(1)
    
    dat_file = sys.argv[1]
    strategy = sys.argv[2] if len(sys.argv) > 2 else "minimal"
    
    if strategy not in ["minimal", "preserve"]:
        print(f"错误: 未知策略 '{strategy}'")
        sys.exit(1)
    
    try:
        generator = IVRSPhantomGeneratorDAT(dat_file)
        generator.parse_dat_file()
        generator.save_phantom_ivrs(strategy=strategy)
        
        # 可选：同时生成两种版本
        if input("\n是否同时生成另一种策略的版本？(y/n): ").lower() == 'y':
            other_strategy = "preserve" if strategy == "minimal" else "minimal"
            generator.save_phantom_ivrs(strategy=other_strategy)
            
    except Exception as e:
        print(f"发生错误: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()