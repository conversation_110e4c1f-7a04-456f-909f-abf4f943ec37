@echo off
echo OpenCore 日志设置脚本
echo ========================

echo.
echo 正在检查 OpenCore 目录结构...

REM 检查当前目录是否包含 EFI 文件夹
if not exist "EFI" (
    echo 错误：当前目录没有找到 EFI 文件夹
    echo 请确保在 OpenCore U 盘根目录运行此脚本
    pause
    exit /b 1
)

if not exist "EFI\OC" (
    echo 错误：没有找到 EFI\OC 目录
    echo 请确保这是一个有效的 OpenCore U 盘
    pause
    exit /b 1
)

echo ✓ 找到 EFI\OC 目录

REM 创建 Logs 目录
if not exist "EFI\OC\Logs" (
    echo 正在创建 Logs 目录...
    mkdir "EFI\OC\Logs"
    if exist "EFI\OC\Logs" (
        echo ✓ Logs 目录创建成功
    ) else (
        echo ✗ Logs 目录创建失败
        pause
        exit /b 1
    )
) else (
    echo ✓ Logs 目录已存在
)

REM 创建测试文件以验证写入权限
echo 正在测试写入权限...
echo Test log file > "EFI\OC\Logs\test.txt"
if exist "EFI\OC\Logs\test.txt" (
    echo ✓ 写入权限正常
    del "EFI\OC\Logs\test.txt"
) else (
    echo ✗ 写入权限有问题
    echo 请检查 U 盘是否为只读状态
    pause
    exit /b 1
)

REM 检查关键文件
echo.
echo 检查关键文件...
if exist "EFI\OC\config.plist" (
    echo ✓ config.plist 存在
) else (
    echo ✗ config.plist 不存在
)

if exist "EFI\OC\ACPI\IVRS.aml" (
    echo ✓ IVRS.aml 存在
) else (
    echo ✗ IVRS.aml 不存在
    echo 请确保已将 IVRS_native.aml 重命名为 IVRS.aml 并放入 ACPI 目录
)

echo.
echo 目录结构检查完成！
echo.
echo 当前目录结构：
tree /F EFI\OC 2>nul || dir /S EFI\OC

echo.
echo 设置完成！现在可以重启测试日志功能。
echo 日志文件将保存在：EFI\OC\Logs\
echo.
pause
