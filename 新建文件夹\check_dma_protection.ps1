# Windows DMA 保护和 IOMMU 状态检查脚本
Write-Host "=== Windows DMA 保护和 IOMMU 状态检查 ===" -ForegroundColor Green
Write-Host ""

# 检查 DMA 保护状态
Write-Host "1. 检查 DMA 保护状态..." -ForegroundColor Yellow
try {
    $dmaProtection = Get-CimInstance -ClassName Win32_DeviceGuard -Namespace root\Microsoft\Windows\DeviceGuard
    if ($dmaProtection) {
        Write-Host "DMA 保护可用性: $($dmaProtection.VirtualizationBasedSecurityHardwareRequirementState)" -ForegroundColor Cyan
        Write-Host "安全启动状态: $($dmaProtection.SecureBootRequired)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "无法获取 DMA 保护状态" -ForegroundColor Red
}

# 检查内存完整性
Write-Host ""
Write-Host "2. 检查内存完整性状态..." -ForegroundColor Yellow
try {
    $hvci = Get-CimInstance -ClassName Win32_DeviceGuard -Namespace root\Microsoft\Windows\DeviceGuard
    if ($hvci) {
        Write-Host "HVCI 状态: $($hvci.CodeIntegrityPolicyEnforcementStatus)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "无法获取内存完整性状态" -ForegroundColor Red
}

# 检查 IOMMU 相关设备
Write-Host ""
Write-Host "3. 检查 IOMMU 相关设备..." -ForegroundColor Yellow
try {
    $iommuDevices = Get-CimInstance -ClassName Win32_PnPEntity | Where-Object { 
        $_.Name -like "*IOMMU*" -or 
        $_.Name -like "*AMD-Vi*" -or 
        $_.Name -like "*VT-d*" -or
        $_.DeviceID -like "*ACPI\IVRS*" -or
        $_.DeviceID -like "*ACPI\DMAR*"
    }
    
    if ($iommuDevices) {
        foreach ($device in $iommuDevices) {
            Write-Host "找到设备: $($device.Name)" -ForegroundColor Green
            Write-Host "  设备ID: $($device.DeviceID)" -ForegroundColor Gray
            Write-Host "  状态: $($device.Status)" -ForegroundColor Gray
        }
    } else {
        Write-Host "未找到 IOMMU 相关设备" -ForegroundColor Red
    }
} catch {
    Write-Host "检查 IOMMU 设备时出错" -ForegroundColor Red
}

# 检查 ACPI 表
Write-Host ""
Write-Host "4. 检查 ACPI 表..." -ForegroundColor Yellow
try {
    $acpiTables = Get-CimInstance -ClassName Win32_SystemEnclosure
    Write-Host "系统制造商: $($acpiTables.Manufacturer)" -ForegroundColor Cyan
    
    # 尝试查找 IVRS 相关的注册表项
    $ivrsKeys = Get-ChildItem -Path "HKLM:\SYSTEM\CurrentControlSet\Enum\ACPI" -ErrorAction SilentlyContinue | 
                Where-Object { $_.Name -like "*IVRS*" -or $_.Name -like "*AMD*" }
    
    if ($ivrsKeys) {
        Write-Host "找到 ACPI IVRS 相关项:" -ForegroundColor Green
        foreach ($key in $ivrsKeys) {
            Write-Host "  $($key.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "未在注册表中找到 IVRS 相关项" -ForegroundColor Red
    }
} catch {
    Write-Host "检查 ACPI 表时出错" -ForegroundColor Red
}

# 检查虚拟化功能
Write-Host ""
Write-Host "5. 检查虚拟化功能..." -ForegroundColor Yellow
try {
    $processor = Get-CimInstance -ClassName Win32_Processor
    Write-Host "处理器: $($processor.Name)" -ForegroundColor Cyan
    Write-Host "虚拟化固件启用: $($processor.VirtualizationFirmwareEnabled)" -ForegroundColor Cyan
    Write-Host "VM 监视器模式扩展: $($processor.VMMonitorModeExtensions)" -ForegroundColor Cyan
} catch {
    Write-Host "检查虚拟化功能时出错" -ForegroundColor Red
}

# 检查事件日志中的相关信息
Write-Host ""
Write-Host "6. 检查事件日志..." -ForegroundColor Yellow
try {
    $events = Get-WinEvent -FilterHashtable @{LogName='System'; ID=12} -MaxEvents 10 -ErrorAction SilentlyContinue |
              Where-Object { $_.Message -like "*IOMMU*" -or $_.Message -like "*DMA*" -or $_.Message -like "*IVRS*" }
    
    if ($events) {
        Write-Host "找到相关事件日志:" -ForegroundColor Green
        foreach ($event in $events) {
            Write-Host "  时间: $($event.TimeCreated)" -ForegroundColor Gray
            Write-Host "  消息: $($event.Message)" -ForegroundColor Gray
            Write-Host ""
        }
    } else {
        Write-Host "未找到相关事件日志" -ForegroundColor Red
    }
} catch {
    Write-Host "检查事件日志时出错" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 检查完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "如果 DMA 保护显示为未启用，请检查：" -ForegroundColor Yellow
Write-Host "1. BIOS 中是否启用了 IOMMU/VT-d" -ForegroundColor White
Write-Host "2. OpenCore 中的 IVRS.aml 是否正确加载" -ForegroundColor White
Write-Host "3. Windows 安全中心 → 设备安全性 → 核心隔离" -ForegroundColor White
Write-Host ""
