#!/usr/bin/env python3
"""
模式2增强处理器 - 将现有DMAR/IVRS表转换为模式2版本
基于boot.efi的模式2实现，生成完全兼容的模式2表
"""

import struct
import os

def calculate_checksum(data):
    """计算ACPI表校验和"""
    checksum = 0
    for byte in data:
        checksum = (checksum + byte) & 0xFF
    return (0x100 - checksum) & 0xFF

def create_mode2_dmar_from_original(original_data):
    """从原始DMAR表创建模式2版本"""
    print("\n=== 创建模式2 DMAR表 ===")
    
    # ACPI表头至少36字节
    if len(original_data) < 36:
        print("错误: 原始DMAR表太小")
        return None
    
    # --- FIX: 从原始表中采集OEM信息 ---
    # ACPI表头中, OemID在偏移量10处, 长度6字节
    # OemTableID在偏移量16处, 长度8字节
    original_oem_id = original_data[10:16]
    original_oem_table_id = original_data[16:24]
    
    print(f"采集到原始OemID: {original_oem_id.decode(errors='ignore').strip()}")
    print(f"采集到原始OemTableID: {original_oem_table_id.decode(errors='ignore').strip()}")
    
    # 创建模式2 DMAR表头
    mode2_dmar = bytearray()
    
    # ACPI表头 (36字节)
    mode2_dmar.extend(b'DMAR')                    # 签名
    mode2_dmar.extend(struct.pack('<I', 0))       # 长度 (稍后更新)
    mode2_dmar.extend(struct.pack('<B', 2))       # 版本2 (模式2标识)
    mode2_dmar.extend(struct.pack('<B', 0))       # 校验和 (稍后计算)
    mode2_dmar.extend(original_oem_id)           # 使用采集的OEM ID
    mode2_dmar.extend(original_oem_table_id)     # 使用采集的OEM Table ID
    mode2_dmar.extend(struct.pack('<I', 2))       # OEM版本 (模式2)
    mode2_dmar.extend(b'VTD2')                    # 创建者ID
    mode2_dmar.extend(struct.pack('<I', 2))       # 创建者版本
    
    # DMAR特定字段
    mode2_dmar.extend(struct.pack('<B', 48))      # 主机地址宽度 (模式2: 48位)
    mode2_dmar.extend(struct.pack('<B', 0x07))    # 标志 (INTR_REMAP + X2APIC + 模式2)
    mode2_dmar.extend(b'\x00' * 10)               # 保留字段
    
    # 添加模式2 DRHD条目
    mode2_bases = [0xFED90000, 0xFED80000, 0xFED91000, 0xFED92000]
    
    for i, base_addr in enumerate(mode2_bases):
        # DRHD条目头
        mode2_dmar.extend(struct.pack('<H', 0))        # 类型: DRHD
        mode2_dmar.extend(struct.pack('<H', 32))       # 长度: 32字节 (DRHD Base + Extensions + Device Scope)
        mode2_dmar.extend(struct.pack('<B', 0x01))     # 标志: INCLUDE_PCI_ALL
        mode2_dmar.extend(struct.pack('<B', 0))        # 保留
        mode2_dmar.extend(struct.pack('<H', 0))        # 段号
        mode2_dmar.extend(struct.pack('<Q', base_addr)) # 寄存器基地址
        
        # 模式2扩展字段
        mode2_dmar.extend(struct.pack('<I', 0x00000002)) # 模式2标识
        mode2_dmar.extend(struct.pack('<I', i + 1))      # IOMMU ID

        # FIX: 添加缺失的8字节Device Scope条目
        mode2_dmar.extend(struct.pack('<B', 1))        # 类型: 1 (PCI Endpoint)
        mode2_dmar.extend(struct.pack('<B', 8))        # 长度: 8
        mode2_dmar.extend(struct.pack('<H', 0))        # 保留
        mode2_dmar.extend(struct.pack('<B', 0))        # 枚举ID
        mode2_dmar.extend(struct.pack('<B', 0))        # 起始总线号
        mode2_dmar.extend(struct.pack('<H', 0))        # PCI路径 (设备0, 功能0)

    # 添加模式2 RMRR条目
    for i in range(4):
        mode2_dmar.extend(struct.pack('<H', 1))          # 类型: RMRR
        mode2_dmar.extend(struct.pack('<H', 24))         # 长度
        mode2_dmar.extend(struct.pack('<H', 0))          # 段号
        mode2_dmar.extend(b'\x00' * 2)                  # 保留
        mode2_dmar.extend(struct.pack('<Q', 0x100000 * i)) # 基地址
        mode2_dmar.extend(struct.pack('<Q', 0x100000 * (i + 1) -1)) # 结束地址
    
    # 更新表长度
    table_length = len(mode2_dmar)
    struct.pack_into('<I', mode2_dmar, 4, table_length)
    
    # 计算校验和
    mode2_dmar[9] = 0
    checksum = calculate_checksum(mode2_dmar)
    mode2_dmar[9] = checksum
    
    print(f"模式2 DMAR表创建完成: {len(mode2_dmar)} 字节")
    print(f"包含 {len(mode2_bases)} 个DRHD条目")
    print(f"包含 4 个RMRR条目")
    
    return bytes(mode2_dmar)

def create_mode2_ivrs_from_original(original_data):
    """从原始IVRS表创建模式2版本"""
    print("\n=== 创建模式2 IVRS表 ===")
    
    # ACPI表头至少36字节
    if len(original_data) < 36:
        print("错误: 原始IVRS表太小")
        return None

    # --- FIX: 从原始表中采集OEM信息 ---
    original_oem_id = original_data[10:16]
    original_oem_table_id = original_data[16:24]

    print(f"采集到原始OemID: {original_oem_id.decode(errors='ignore').strip()}")
    print(f"采集到原始OemTableID: {original_oem_table_id.decode(errors='ignore').strip()}")
    
    # 创建模式2 IVRS表头
    mode2_ivrs = bytearray()
    
    # ACPI表头 (36字节)
    mode2_ivrs.extend(b'IVRS')                    # 签名
    mode2_ivrs.extend(struct.pack('<I', 0))       # 长度 (稍后更新)
    mode2_ivrs.extend(struct.pack('<B', 2))       # 版本2 (模式2标识)
    mode2_ivrs.extend(struct.pack('<B', 0))       # 校验和 (稍后计算)
    mode2_ivrs.extend(original_oem_id)           # 使用采集的OEM ID
    mode2_ivrs.extend(original_oem_table_id)     # 使用采集的OEM Table ID
    mode2_ivrs.extend(struct.pack('<I', 2))       # OEM版本 (模式2)
    mode2_ivrs.extend(b'AMD2')                    # 创建者ID
    mode2_ivrs.extend(struct.pack('<I', 2))       # 创建者版本
    
    # IVRS特定字段
    mode2_ivrs.extend(struct.pack('<I', 0x00400022)) # IVInfo (模式2信息)
    mode2_ivrs.extend(struct.pack('<Q', 0))       # 保留字段
    
    # 添加模式2 IVHD条目
    mode2_bases = [0xFEC00000, 0xFEC10000, 0xFEC20000]
    
    for i, base_addr in enumerate(mode2_bases):
        # IVHD条目头
        mode2_ivrs.extend(struct.pack('<B', 0x10))     # 类型: IVHD
        mode2_ivrs.extend(struct.pack('<B', 0x02))     # 标志: 模式2
        mode2_ivrs.extend(struct.pack('<H', 36))       # 长度: 36字节 (IVHD Base + Extensions + Device Entry)
        mode2_ivrs.extend(struct.pack('<H', 0x0002 + i)) # 设备ID
        mode2_ivrs.extend(struct.pack('<H', 0x40))     # 能力偏移
        mode2_ivrs.extend(struct.pack('<Q', base_addr)) # IOMMU基地址
        mode2_ivrs.extend(struct.pack('<H', 0))        # PCI段组
        mode2_ivrs.extend(struct.pack('<H', 0x0002))   # IOMMU信息 (模式2)
        mode2_ivrs.extend(struct.pack('<I', 0x00000002)) # IOMMU属性 (模式2)
        
        # 模式2扩展字段
        mode2_ivrs.extend(struct.pack('<I', 0x00000002)) # 模式2标识
        mode2_ivrs.extend(struct.pack('<I', i + 1))      # IOMMU ID

        # FIX: 添加缺失的4字节Device Entry (Select All)
        mode2_ivrs.extend(struct.pack('<B', 1))        # 类型: 1 (Select All)
        mode2_ivrs.extend(struct.pack('<H', 0))        # 设备ID (0 for all)
        mode2_ivrs.extend(struct.pack('<B', 0))        # 数据设置
    
    # 添加模式2 IVMD条目 (内存定义)
    mode2_ivrs.extend(struct.pack('<B', 0x20))         # 类型: IVMD
    mode2_ivrs.extend(struct.pack('<B', 0x02))         # 标志: 模式2
    mode2_ivrs.extend(struct.pack('<H', 32))           # 长度: 32字节
    mode2_ivrs.extend(struct.pack('<H', 0))            # 设备ID
    mode2_ivrs.extend(struct.pack('<H', 0))            # 辅助数据
    mode2_ivrs.extend(struct.pack('<Q', 0))            # 保留
    mode2_ivrs.extend(struct.pack('<Q', 0x0000000000000000)) # 内存起始地址
    mode2_ivrs.extend(struct.pack('<Q', 0x0000000100000000)) # 内存长度 (4GB)
    
    # 更新表长度
    table_length = len(mode2_ivrs)
    struct.pack_into('<I', mode2_ivrs, 4, table_length)
    
    # 计算校验和
    mode2_ivrs[9] = 0
    checksum = calculate_checksum(mode2_ivrs)
    mode2_ivrs[9] = checksum
    
    print(f"模式2 IVRS表创建完成: {len(mode2_ivrs)} 字节")
    print(f"包含 {len(mode2_bases)} 个IVHD条目")
    print(f"包含 1 个IVMD条目")
    
    return bytes(mode2_ivrs)

def main():
    """主函数"""
    print("模式2增强处理器")
    print("将现有DMAR/IVRS表转换为完整的模式2版本")
    print("="*60)
    
    # 注意: 您可能需要更改以下路径以匹配您的文件位置
    input_dir = 'dat_original'  # 建议将原始文件放在一个子目录中
    output_dir = 'dat_generated'
    
    # 创建目录(如果不存在)
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    dmar_path = os.path.join(input_dir, "dmar.dat")
    ivrs_path = os.path.join(input_dir, "ivrs.dat")

    # 如果原始文件不存在，创建虚拟文件以供测试
    if not os.path.exists(dmar_path):
        print(f"未找到 {dmar_path}，创建一个虚拟文件。")
        with open(dmar_path, 'wb') as f:
            f.write(b'DMAR' + b'\x24\x00\x00\x00' + b'\x01' + b'\x00' + b'ORIGDM' + b'TABLE001' + b'\x00' * 12)
            
    if not os.path.exists(ivrs_path):
        print(f"未找到 {ivrs_path}，创建一个虚拟文件。")
        with open(ivrs_path, 'wb') as f:
            f.write(b'IVRS' + b'\x24\x00\x00\x00' + b'\x01' + b'\x00' + b'ORIGIV' + b'TABLE002' + b'\x00' * 12)

    # 处理DMAR文件
    if os.path.exists(dmar_path):
        with open(dmar_path, 'rb') as f:
            original_dmar = f.read()
        
        mode2_dmar = create_mode2_dmar_from_original(original_dmar)
        if mode2_dmar:
            output_path = os.path.join(output_dir, 'dmar_mode2.dat')
            with open(output_path, 'wb') as f:
                f.write(mode2_dmar)
            print(f"模式2 DMAR表已保存到: {output_path}")
    
    # 处理IVRS文件
    if os.path.exists(ivrs_path):
        with open(ivrs_path, 'rb') as f:
            original_ivrs = f.read()
        
        mode2_ivrs = create_mode2_ivrs_from_original(original_ivrs)
        if mode2_ivrs:
            output_path = os.path.join(output_dir, 'ivrs_mode2.dat')
            with open(output_path, 'wb') as f:
                f.write(mode2_ivrs)
            print(f"模式2 IVRS表已保存到: {output_path}")

    print("\n=== 模式2处理完成 ===")

if __name__ == "__main__":
    main()