#!/usr/bin/env python3
"""
模式2增强处理器 - 将现有DMAR/IVRS表转换为模式2版本
基于boot.efi的模式2实现，生成完全兼容的模式2表
"""

import struct
import os

def parse_acpi_header(data, offset=0):
    """解析ACPI表头"""
    if len(data) < offset + 36:
        return None

    header = {}
    header['signature'] = data[offset:offset+4].decode('ascii', errors='ignore')
    header['length'] = struct.unpack('<I', data[offset+4:offset+8])[0]
    header['revision'] = data[offset+8]
    header['checksum'] = data[offset+9]
    header['oemid'] = data[offset+10:offset+16].decode('ascii', errors='ignore').strip()
    header['oem_table_id'] = data[offset+16:offset+24].decode('ascii', errors='ignore').strip()
    header['oem_revision'] = struct.unpack('<I', data[offset+24:offset+28])[0]
    header['creator_id'] = data[offset+28:offset+32].decode('ascii', errors='ignore')
    header['creator_revision'] = struct.unpack('<I', data[offset+32:offset+36])[0]

    return header

def calculate_checksum(data):
    """计算ACPI表校验和"""
    checksum = 0
    for byte in data:
        checksum = (checksum + byte) & 0xFF
    return (0x100 - checksum) & 0xFF

def create_mode2_dmar_from_original(original_data):
    """从原始DMAR表创建模式2版本"""
    print("\n=== 创建模式2 DMAR表 ===")

    # 解析原始表头
    header = parse_acpi_header(original_data)
    if not header:
        print("错误: 无法解析原始DMAR表头")
        return None

    print(f"原始DMAR表信息:")
    print(f"  OEM ID: \"{header['oemid']}\"")
    print(f"  OEM表ID: \"{header['oem_table_id']}\"")
    print(f"  OEM版本: {header['oem_revision']}")
    print(f"  创建者ID: \"{header['creator_id']}\"")
    print(f"  创建者版本: {header['creator_revision']}")

    # 创建模式2 DMAR表头
    mode2_dmar = bytearray()

    # ACPI表头 (完全保留原始信息)
    mode2_dmar.extend(b'DMAR')                    # 签名
    mode2_dmar.extend(struct.pack('<I', 0))       # 长度 (稍后更新)
    mode2_dmar.extend(struct.pack('<B', 2))       # 版本2 (模式2标识)
    mode2_dmar.extend(struct.pack('<B', 0))       # 校验和 (稍后计算)
    mode2_dmar.extend(header['oemid'].ljust(6, ' ')[:6].encode('ascii'))  # 保留原始OEM ID
    mode2_dmar.extend(header['oem_table_id'].ljust(8, ' ')[:8].encode('ascii'))  # 保留原始OEM表ID
    mode2_dmar.extend(struct.pack('<I', header['oem_revision']))  # 保留原始OEM版本
    mode2_dmar.extend(header['creator_id'].ljust(4, ' ')[:4].encode('ascii'))  # 保留创建者ID
    mode2_dmar.extend(struct.pack('<I', header['creator_revision']))  # 保留创建者版本
    
    # DMAR特定字段
    mode2_dmar.extend(struct.pack('<B', 48))      # 主机地址宽度 (模式2: 48位)
    mode2_dmar.extend(struct.pack('<B', 0x07))    # 标志 (INTR_REMAP + X2APIC + 模式2)
    mode2_dmar.extend(b'\x00' * 10)               # 保留字段
    
    # 添加模式2 DRHD条目 (基于boot.efi分析的4个基地址)
    mode2_bases = [0xFED90000, 0xFED80000, 0xFED91000, 0xFED92000]
    
    for i, base_addr in enumerate(mode2_bases):
        # DRHD条目头
        mode2_dmar.extend(struct.pack('<H', 0))        # 类型: DRHD
        mode2_dmar.extend(struct.pack('<H', 32))       # 长度: 32字节 (DRHD Base + Extensions + Device Scope)
        mode2_dmar.extend(struct.pack('<B', 0x01))     # 标志: INCLUDE_PCI_ALL
        mode2_dmar.extend(struct.pack('<B', 0))        # 保留
        mode2_dmar.extend(struct.pack('<H', 0))        # 段号
        mode2_dmar.extend(struct.pack('<Q', base_addr)) # 寄存器基地址
        
        # 模式2扩展字段
        mode2_dmar.extend(struct.pack('<I', 0x00000002)) # 模式2标识
        mode2_dmar.extend(struct.pack('<I', i + 1))      # IOMMU ID

        # FIX: 添加缺失的8字节Device Scope条目
        mode2_dmar.extend(struct.pack('<B', 1))        # 类型: 1 (PCI Endpoint)
        mode2_dmar.extend(struct.pack('<B', 8))        # 长度: 8
        mode2_dmar.extend(struct.pack('<H', 0))        # 保留
        mode2_dmar.extend(struct.pack('<B', 0))        # 枚举ID
        mode2_dmar.extend(struct.pack('<B', 0))        # 起始总线号
        mode2_dmar.extend(struct.pack('<H', 0))        # PCI路径 (设备0, 功能0)

    # 添加模式2 RMRR条目 (基于分析的423次使用)
    # This part seems fine, no changes needed
    for i in range(4):  # 添加4个RMRR条目
        mode2_dmar.extend(struct.pack('<H', 1))          # 类型: RMRR
        mode2_dmar.extend(struct.pack('<H', 32))         # 长度
        mode2_dmar.extend(struct.pack('<H', 0))          # 段号
        mode2_dmar.extend(b'\x00' * 10)                  # 保留
        mode2_dmar.extend(struct.pack('<Q', 0x100000 * i))      # 基地址
        mode2_dmar.extend(struct.pack('<Q', 0x100000 * (i + 1) -1)) # 结束地址
    
    # 更新表长度
    table_length = len(mode2_dmar)
    struct.pack_into('<I', mode2_dmar, 4, table_length)
    
    # 计算校验和
    mode2_dmar[9] = 0  # 清零校验和字段
    checksum = calculate_checksum(mode2_dmar)
    mode2_dmar[9] = checksum
    
    print(f"模式2 DMAR表创建完成: {len(mode2_dmar)} 字节")
    print(f"包含 {len(mode2_bases)} 个DRHD条目")
    print(f"包含 4 个RMRR条目")
    
    return bytes(mode2_dmar)

def create_mode2_ivrs_from_original(original_data):
    """从原始IVRS表创建模式2版本"""
    print("\n=== 创建模式2 IVRS表 ===")
    
    # 解析原始表头
    if len(original_data) < 36: # An IVRS header is 36 bytes
        print("错误: 原始IVRS表太小")
        return None
    
    # 创建模式2 IVRS表头
    mode2_ivrs = bytearray()
    
    # ACPI表头 (36字节)
    mode2_ivrs.extend(b'IVRS')                    # 签名
    mode2_ivrs.extend(struct.pack('<I', 0))       # 长度 (稍后更新)
    mode2_ivrs.extend(struct.pack('<B', 2))       # 版本2 (模式2标识)
    mode2_ivrs.extend(struct.pack('<B', 0))       # 校验和 (稍后计算)
    mode2_ivrs.extend(b'AMD   ')                  # OEM ID
    mode2_ivrs.extend(b'AMDVI002')                # OEM表ID (AMDVI002标识)
    mode2_ivrs.extend(struct.pack('<I', 2))       # OEM版本 (模式2)
    mode2_ivrs.extend(b'AMD2')                    # 创建者ID
    mode2_ivrs.extend(struct.pack('<I', 2))       # 创建者版本
    
    # IVRS特定字段
    mode2_ivrs.extend(struct.pack('<I', 0x00400022)) # IVInfo (模式2信息)
    mode2_ivrs.extend(struct.pack('<Q', 0))       # 保留字段
    
    # 添加模式2 IVHD条目 (基于boot.efi分析的3个基地址)
    mode2_bases = [0xFEC00000, 0xFEC10000, 0xFEC20000]
    
    for i, base_addr in enumerate(mode2_bases):
        # IVHD条目头
        mode2_ivrs.extend(struct.pack('<B', 0x10))     # 类型: IVHD
        mode2_ivrs.extend(struct.pack('<B', 0x02))     # 标志: 模式2
        mode2_ivrs.extend(struct.pack('<H', 36))       # 长度: 36字节 (IVHD Base + Extensions + Device Entry)
        mode2_ivrs.extend(struct.pack('<H', 0x0002 + i)) # 设备ID
        mode2_ivrs.extend(struct.pack('<H', 0x40))     # 能力偏移
        mode2_ivrs.extend(struct.pack('<Q', base_addr)) # IOMMU基地址
        mode2_ivrs.extend(struct.pack('<H', 0))        # PCI段组
        mode2_ivrs.extend(struct.pack('<H', 0x0002))   # IOMMU信息 (模式2)
        mode2_ivrs.extend(struct.pack('<I', 0x00000002)) # IOMMU属性 (模式2)
        
        # 模式2扩展字段
        mode2_ivrs.extend(struct.pack('<I', 0x00000002)) # 模式2标识
        mode2_ivrs.extend(struct.pack('<I', i + 1))      # IOMMU ID

        # FIX: 添加缺失的4字节Device Entry (Select All)
        mode2_ivrs.extend(struct.pack('<B', 1))        # 类型: 1 (Select All)
        mode2_ivrs.extend(struct.pack('<H', 0))        # 设备ID (0 for all)
        mode2_ivrs.extend(struct.pack('<B', 0))        # 数据设置
    
    # 添加模式2 IVMD条目 (内存定义)
    # This part seems fine, no changes needed
    mode2_ivrs.extend(struct.pack('<B', 0x20))         # 类型: IVMD
    mode2_ivrs.extend(struct.pack('<B', 0x02))         # 标志: 模式2
    mode2_ivrs.extend(struct.pack('<H', 32))           # 长度: 32字节
    mode2_ivrs.extend(struct.pack('<H', 0))            # 设备ID
    mode2_ivrs.extend(struct.pack('<H', 0))            # 辅助数据
    mode2_ivrs.extend(struct.pack('<Q', 0))            # 保留
    mode2_ivrs.extend(struct.pack('<Q', 0x0000000000000000)) # 内存起始地址
    mode2_ivrs.extend(struct.pack('<Q', 0x0000000100000000)) # 内存长度 (4GB)
    
    # 更新表长度
    table_length = len(mode2_ivrs)
    struct.pack_into('<I', mode2_ivrs, 4, table_length)
    
    # 计算校验和
    mode2_ivrs[9] = 0  # 清零校验和字段
    checksum = calculate_checksum(mode2_ivrs)
    mode2_ivrs[9] = checksum
    
    print(f"模式2 IVRS表创建完成: {len(mode2_ivrs)} 字节")
    print(f"包含 {len(mode2_bases)} 个IVHD条目")
    print(f"包含 1 个IVMD条目")
    print(f"AMDVI002标识: 已设置")
    
    return bytes(mode2_ivrs)

# The rest of the script remains the same
def create_efi_protocol_hook_demo():
    """创建EFI协议Hook演示代码"""
    print("\n=== EFI协议Hook系统演示 ===")
    
    hook_code = '''
// EFI协议Hook系统实现示例
// 基于boot.efi分析的7种协议Hook机制

#include <Uefi.h>
#include <Protocol/AcpiTable.h>
#include <Protocol/PciIo.h>

// 协议Hook结构
typedef struct {
    EFI_GUID *ProtocolGuid;
    VOID *OriginalInterface;
    VOID *HookedInterface;
    BOOLEAN IsActive;
} PROTOCOL_HOOK_ENTRY;

// 全局Hook表 (7种协议)
PROTOCOL_HOOK_ENTRY gProtocolHooks[7];

// 1. ACPI表协议Hook (3次LocateProtocol调用)
EFI_STATUS EFIAPI HookedInstallAcpiTable(
    IN EFI_ACPI_TABLE_PROTOCOL *This,
    IN VOID *AcpiTableBuffer,
    IN UINTN AcpiTableBufferSize,
    OUT UINTN *TableKey
) {
    // 检查是否为VT-d相关表
    UINT32 *Signature = (UINT32*)AcpiTableBuffer;
    
    if (*Signature == SIGNATURE_32('D', 'M', 'A', 'R') ||
        *Signature == SIGNATURE_32('I', 'V', 'R', 'S')) {
        // 拦截VT-d表安装，注入模式2版本
        return InjectMode2Table(AcpiTableBuffer, AcpiTableBufferSize, TableKey);
    }
    
    // 调用原始函数
    EFI_ACPI_TABLE_PROTOCOL *Original = 
        (EFI_ACPI_TABLE_PROTOCOL*)gProtocolHooks[0].OriginalInterface;
    return Original->InstallAcpiTable(This, AcpiTableBuffer, AcpiTableBufferSize, TableKey);
}

// 2. PCI I/O协议Hook (4次HandleProtocol调用)
EFI_STATUS EFIAPI HookedPciRead(
    IN EFI_PCI_IO_PROTOCOL *This,
    IN EFI_PCI_IO_PROTOCOL_WIDTH Width,
    IN UINT32 Offset,
    IN UINTN Count,
    IN OUT VOID *Buffer
) {
    // 检查是否访问VT-d相关寄存器
    if (IsVTdRegisterAccess(Offset)) {
        return EmulateVTdRegisterRead(Offset, Width, Count, Buffer);
    }
    
    // 调用原始函数
    EFI_PCI_IO_PROTOCOL *Original = 
        (EFI_PCI_IO_PROTOCOL*)gProtocolHooks[1].OriginalInterface;
    return Original->Pci.Read(This, Width, Offset, Count, Buffer);
}

// Hook安装函数
EFI_STATUS InstallProtocolHooks(VOID) {
    EFI_STATUS Status;
    
    // Hook ACPI表协议
    Status = gBS->LocateProtocol(&gEfiAcpiTableProtocolGuid, NULL, 
                                &gProtocolHooks[0].OriginalInterface);
    if (!EFI_ERROR(Status)) {
        // 创建Hook接口
        // ... Hook安装逻辑
        gProtocolHooks[0].IsActive = TRUE;
    }
    
    // Hook其他6种协议...
    
    return EFI_SUCCESS;
}
'''
    
    print("EFI协议Hook代码已生成")
    return hook_code

def main():
    """主函数"""
    print("模式2增强处理器")
    print("将现有DMAR/IVRS表转换为完整的模式2版本")
    print("="*60)
    
    # Create dummy directories if they don't exist
    if not os.path.exists('dat'):
        os.makedirs('dat')

    # Create dummy original files for testing if they don't exist
    dmar_path = r"dat/dmar.dat"
    if not os.path.exists(dmar_path):
        with open(dmar_path, 'wb') as f:
            # Create a minimal DMAR header
            f.write(b'DMAR' + b'\x24\x00\x00\x00' + b'\x01' * 28)
            print(f"创建了虚拟的 {dmar_path}")


    ivrs_path = r"dat/ivrs.dat"
    if not os.path.exists(ivrs_path):
        with open(ivrs_path, 'wb') as f:
            # Create a minimal IVRS header
            f.write(b'IVRS' + b'\x24\x00\x00\x00' + b'\x01' * 28)
            print(f"创建了虚拟的 {ivrs_path}")

    # 处理DMAR文件
    with open(dmar_path, 'rb') as f:
        original_dmar = f.read()
    
    mode2_dmar = create_mode2_dmar_from_original(original_dmar)
    if mode2_dmar:
        # 保存模式2 DMAR表
        with open('dat/dmar_mode2.dat', 'wb') as f:
            f.write(mode2_dmar)
        print("模式2 DMAR表已保存到: dat/dmar_mode2.dat")
    
    # 处理IVRS文件
    with open(ivrs_path, 'rb') as f:
        original_ivrs = f.read()
    
    mode2_ivrs = create_mode2_ivrs_from_original(original_ivrs)
    if mode2_ivrs:
        # 保存模式2 IVRS表
        with open('dat/ivrs_mode2.dat', 'wb') as f:
            f.write(mode2_ivrs)
        print("模式2 IVRS表已保存到: dat/ivrs_mode2.dat")
    
    # 创建EFI协议Hook演示
    hook_code = create_efi_protocol_hook_demo()
    with open('EFI_Protocol_Hook_Demo.c', 'w') as f:
        f.write(hook_code)
    print("EFI协议Hook演示代码已保存到: EFI_Protocol_Hook_Demo.c")
    
    print("\n=== 模式2处理完成 ===")
    print("生成的文件:")
    print("1. dat/dmar_mode2.dat - 模式2 DMAR表")
    print("2. dat/ivrs_mode2.dat - 模式2 IVRS表") 
    print("3. EFI_Protocol_Hook_Demo.c - EFI协议Hook演示")

if __name__ == "__main__":
    main()