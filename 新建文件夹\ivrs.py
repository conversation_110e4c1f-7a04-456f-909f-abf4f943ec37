#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IVRS.rw 到 IVRS.asl 自动转换器
用于OpenCore AMD IOMMU欺骗表生成
基于真实硬件数据，使用安全的"零设备管理"策略
核心策略：伪装IOMMU已启用，但NumberOfDeviceEntries=0确保不卡开机logo
"""

import re
import sys
import os
from datetime import datetime
from pathlib import Path

# ACPI表结构常量
ACPI_HEADER_SIZE = 36
IVRS_HEADER_SIZE = 12
IVHD_HEADER_SIZE = 24

class IVRSGenerator:
    """
    解析IVRS.rw文件并生成一个安全的、用于仿真的ivrs_custom.asl文件。
    核心功能：
    1. 提取真实硬件签名信息以提高兼容性
    2. 提取真实IOMMU基地址和参数
    3. 生成"零设备管理"的IVRS表（NumberOfDeviceEntries=0）
    4. 确保系统认为IOMMU已启用但不会卡开机logo
    """
    def __init__(self, rw_file_path):
        self.rw_file_path = Path(rw_file_path)
        if not self.rw_file_path.exists():
            raise FileNotFoundError(f"Error: Input file not found at {rw_file_path}")

        # 初始化默认值，确保不会出现None值错误
        self.ivrs_data = {
            'oem_id': "AMD",
            'oem_table_id': "AmdTable",
            'oem_revision': 1,
            'creator_id': "AMD",
            'creator_revision': 1,
            'iv_info': 0x00203041,  # 默认IV Info
            'ivhd_type': 0x11,      # Type 11h
            'ivhd_flags': 0x00,     # 无特殊标志
            'device_id': 0x0002,    # 通用设备ID
            'capability_offset': 0x40,  # 标准能力偏移
            'iommu_base_address': 0xFD500000,  # 标准IOMMU基地址
            'pci_segment': 0x0000,  # PCI段0
            'iommu_info': 0x0000    # 基本信息
        }

    def parse_rw_file(self):
        """解析IVRS.rw文件，提取关键硬件签名信息和IOMMU参数。"""
        try:
            content = self.rw_file_path.read_text(encoding='utf-8', errors='replace')
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试二进制模式读取
            content = self.rw_file_path.read_bytes().decode('latin1')

        print(f"正在解析文件: {self.rw_file_path}")

        # 基本表头信息提取
        basic_patterns = {
            'oem_id': r'OEM ID\s+"([^"]+)"',
            'oem_table_id': r'OEM Table ID\s+"([^"]+)"',
            'oem_revision': r'OEM Revision\s+0x([0-9A-F]+)',
            'creator_id': r'Creator ID\s+"([^"]*)"',
            'creator_revision': r'Creator Revision\s+0x([0-9A-F]+)',
        }

        extracted_count = 0
        for key, pattern in basic_patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                if 'id' in key:
                    self.ivrs_data[key] = value
                else:
                    self.ivrs_data[key] = int(value, 16)
                extracted_count += 1

        print(f"✓ 成功提取 {extracted_count}/{len(basic_patterns)} 个基本参数")

        # 从原始十六进制数据中提取真实IOMMU硬件参数
        self._extract_real_iommu_params(content)

        # 显示最终使用的参数
        self._display_final_params()
        
    def _extract_real_iommu_params(self, content):
        """从IVRS.rw原始数据中提取真实的IOMMU硬件参数"""
        print("正在提取IOMMU硬件参数...")

        # 查找原始二进制数据行
        hex_lines = []
        for line in content.split('\n'):
            if line.strip().startswith('Data\t'):
                # 提取十六进制数据: "Data	0x41 0x30..." -> ['41', '30', ...]
                hex_part = line.split('\t')[1] if '\t' in line else ''
                hex_values = [h.replace('0x', '') for h in hex_part.split() if h.startswith('0x')]
                hex_lines.extend(hex_values)

        if len(hex_lines) >= 16:  # 确保有足够的数据
            try:
                # IVRS特定头信息 (ACPI头后的前12字节)
                # IV Info (4 bytes, 小端序) - 偏移0-3
                if len(hex_lines) >= 4:
                    iv_info = int(hex_lines[3] + hex_lines[2] + hex_lines[1] + hex_lines[0], 16)
                    self.ivrs_data['iv_info'] = iv_info
                    print(f"✓ IV Info: 0x{iv_info:08X}")

                # 寻找第一个IVHD条目 (在IVRS特定头之后)
                # IVRS特定头是12字节，所以IVHD从偏移12开始
                ivhd_start = 12  # IVRS特定头长度
                if len(hex_lines) > ivhd_start + 20:  # 确保有足够数据解析IVHD
                    i = ivhd_start
                    type_byte = int(hex_lines[i], 16)

                    if type_byte == 0x10 or type_byte == 0x11:  # IVHD Type
                        # 提取真实IOMMU参数
                        flags = int(hex_lines[i + 1], 16)
                        ivhd_length = int(hex_lines[i + 2] + hex_lines[i + 3], 16)  # 小端序
                        device_id = int(hex_lines[i + 4] + hex_lines[i + 5], 16)    # 小端序
                        cap_offset = int(hex_lines[i + 6], 16)

                        # IOMMU基地址 (8字节，小端序)
                        iommu_base = 0
                        for j in range(8):
                            if i + 8 + j < len(hex_lines):
                                iommu_base |= int(hex_lines[i + 8 + j], 16) << (j * 8)

                        # PCI段 (2字节，小端序)
                        pci_segment = 0
                        if i + 16 < len(hex_lines) and i + 17 < len(hex_lines):
                            pci_segment = int(hex_lines[i + 16] + hex_lines[i + 17], 16)

                        # IOMMU信息/特性 (2字节，小端序)
                        iommu_info = 0
                        if i + 18 < len(hex_lines) and i + 19 < len(hex_lines):
                            iommu_info = int(hex_lines[i + 18] + hex_lines[i + 19], 16)

                        # 保存提取的真实硬件参数
                        self.ivrs_data.update({
                            'ivhd_type': type_byte,
                            'ivhd_flags': flags,
                            'ivhd_length': ivhd_length,
                            'device_id': device_id,
                            'capability_offset': cap_offset,
                            'iommu_base_address': iommu_base,
                            'pci_segment': pci_segment,
                            'iommu_info': iommu_info
                        })

                        print(f"✓ 采集到真实IOMMU参数:")
                        print(f"  - IVHD Type: 0x{type_byte:02X}")
                        print(f"  - IVHD Flags: 0x{flags:02X}")
                        print(f"  - Device ID: 0x{device_id:04X}")
                        print(f"  - IOMMU Base: 0x{iommu_base:016X}")
                        print(f"  - PCI Segment: 0x{pci_segment:04X}")
                        print(f"  - Capability Offset: 0x{cap_offset:02X}")
                    else:
                        print(f"[WARN] 未找到有效的IVHD条目 (Type: 0x{type_byte:02X})")

            except (ValueError, IndexError, TypeError) as e:
                print(f"[WARN] 无法解析真实IOMMU参数，使用默认值: {e}")
        else:
            print("[WARN] IVRS.rw数据不足，使用默认IOMMU参数")

    def _display_final_params(self):
        """显示最终使用的参数"""
        print("\n=== 最终使用的IVRS参数 ===")
        print(f"OEM ID: '{self.ivrs_data['oem_id']}'")
        print(f"OEM Table ID: '{self.ivrs_data['oem_table_id']}'")
        print(f"OEM Revision: 0x{self.ivrs_data['oem_revision']:08X}")
        print(f"Creator ID: '{self.ivrs_data['creator_id']}'")
        print(f"Creator Revision: 0x{self.ivrs_data['creator_revision']:08X}")
        print(f"IV Info: 0x{self.ivrs_data['iv_info']:08X}")
        print(f"IVHD Type: 0x{self.ivrs_data['ivhd_type']:02X}")
        print(f"IVHD Flags: 0x{self.ivrs_data['ivhd_flags']:02X}")
        print(f"Device ID: 0x{self.ivrs_data['device_id']:04X}")
        print(f"IOMMU Base Address: 0x{self.ivrs_data['iommu_base_address']:016X}")
        print(f"PCI Segment: 0x{self.ivrs_data['pci_segment']:04X}")
        print(f"Capability Offset: 0x{self.ivrs_data['capability_offset']:02X}")
        print("=" * 35)


    def generate_asl(self):
        """根据提取的数据和安全策略，生成ASL文件内容。
        核心策略：使用真实硬件参数但确保NumberOfDeviceEntries=0
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 安全地获取字符串参数，确保不会出现None值错误
        oem_id = str(self.ivrs_data.get('oem_id', 'AMD')).ljust(6)[:6]
        oem_table_id = str(self.ivrs_data.get('oem_table_id', 'AmdTable')).ljust(8)[:8]
        oem_revision = self.ivrs_data.get('oem_revision', 1)
        creator_id = str(self.ivrs_data.get('creator_id', 'AMD')).ljust(4)[:4]
        creator_revision = self.ivrs_data.get('creator_revision', 1)

        # 获取真实的IOMMU参数
        iv_info = self.ivrs_data.get('iv_info', 0x00203041)
        ivhd_type = self.ivrs_data.get('ivhd_type', 0x11)
        ivhd_flags = self.ivrs_data.get('ivhd_flags', 0x00)
        device_id = self.ivrs_data.get('device_id', 0x0002)
        capability_offset = self.ivrs_data.get('capability_offset', 0x40)
        iommu_base_address = self.ivrs_data.get('iommu_base_address', 0xFD500000)
        pci_segment = self.ivrs_data.get('pci_segment', 0x0000)
        iommu_info = self.ivrs_data.get('iommu_info', 0x0000)

        # 计算表长度 (ACPI头 + IVRS特殊头 + 最小IVHD条目)
        # 核心：IVHD条目只包含头部，没有设备条目 = NumberOfDeviceEntries = 0
        ivrs_header_len = IVRS_HEADER_SIZE  # 12字节
        ivhd_entry_len = IVHD_HEADER_SIZE   # 24字节（只有头部，无设备条目）
        total_data_len = ivrs_header_len + ivhd_entry_len
        total_table_len = ACPI_HEADER_SIZE + total_data_len  # 36 + 12 + 24 = 72字节

        # 判断是否使用了真实数据
        using_real_data = hasattr(self, '_extracted_real_data') or (
            self.ivrs_data.get('oem_id') != 'AMD' or
            self.ivrs_data.get('iommu_base_address') != 0xFD500000
        )

        asl_content = f'''/*
 * 自动生成的IVRS表 - 用于OpenCore AMD IOMMU欺骗
 * 生成时间: {timestamp}
 * 硬件签名: {oem_id.strip()} / {oem_table_id.strip()} {'(真实数据)' if using_real_data else '(默认数据)'}
 * IOMMU基址: 0x{iommu_base_address:016X}
 * IV Info: 0x{iv_info:08X}
 * 核心策略: NumberOfDeviceEntries=0 不管理任何设备，确保安全启动
 *
 * 重要说明：
 * - 此表伪装IOMMU已启用，满足系统检测要求
 * - NumberOfDeviceEntries=0确保不会卡在开机logo
 * - 使用真实硬件参数提高兼容性
 */

DefinitionBlock ("", "SSDT", 2, "{oem_id}", "IVRSTBL", 0x00000001)
{{
    /*
     * IVRS Raw Data Table - ACPI compliant structure
     * Total length: {total_table_len} bytes (0x{total_table_len:02X})
     * Creator ID: "{creator_id}" (0x{creator_revision:08X})
     */
    Name (IVRS, Buffer (0x{total_table_len:02X})
    {{
        /* ACPI Standard Header (36 bytes) */
        0x49, 0x56, 0x52, 0x53,                         // Signature: "IVRS"
        0x{total_table_len & 0xFF:02X}, 0x{(total_table_len >> 8) & 0xFF:02X}, 0x{(total_table_len >> 16) & 0xFF:02X}, 0x{(total_table_len >> 24) & 0xFF:02X}, // Length: {total_table_len}
        0x02,                                           // Revision: 2
        0x00,                                           // Checksum (calculated by BIOS)
        0x{ord(oem_id[0]):02X}, 0x{ord(oem_id[1]):02X}, 0x{ord(oem_id[2]):02X}, 0x{ord(oem_id[3]):02X}, 0x{ord(oem_id[4]):02X}, 0x{ord(oem_id[5]):02X}, // OEM ID: "{oem_id}"
        0x{ord(oem_table_id[0]):02X}, 0x{ord(oem_table_id[1]):02X}, 0x{ord(oem_table_id[2]):02X}, 0x{ord(oem_table_id[3]):02X}, 0x{ord(oem_table_id[4]):02X}, 0x{ord(oem_table_id[5]):02X}, 0x{ord(oem_table_id[6]):02X}, 0x{ord(oem_table_id[7]):02X}, // OEM Table ID: "{oem_table_id}"
        0x{oem_revision & 0xFF:02X}, 0x{(oem_revision >> 8) & 0xFF:02X}, 0x{(oem_revision >> 16) & 0xFF:02X}, 0x{(oem_revision >> 24) & 0xFF:02X}, // OEM Revision: 0x{oem_revision:08X}
        0x{ord(creator_id[0]):02X}, 0x{ord(creator_id[1]):02X}, 0x{ord(creator_id[2]):02X}, 0x{ord(creator_id[3]):02X}, // Creator ID: "{creator_id}"
        0x{creator_revision & 0xFF:02X}, 0x{(creator_revision >> 8) & 0xFF:02X}, 0x{(creator_revision >> 16) & 0xFF:02X}, 0x{(creator_revision >> 24) & 0xFF:02X}, // Creator Revision: 0x{creator_revision:08X}

        /* IVRS-specific Header (12 bytes) - 使用真实IV Info */
        0x{iv_info & 0xFF:02X}, 0x{(iv_info >> 8) & 0xFF:02X}, 0x{(iv_info >> 16) & 0xFF:02X}, 0x{(iv_info >> 24) & 0xFF:02X}, // IV Info: 0x{iv_info:08X} (真实)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Reserved fields

        /* IVHD Entry - 使用真实硬件参数但确保NumberOfDeviceEntries=0 */
        0x{ivhd_type:02X},                              // Type: 0x{ivhd_type:02X} (真实IVHD类型)
        0x{ivhd_flags:02X},                             // Flags: 0x{ivhd_flags:02X} (真实标志)
        0x{ivhd_entry_len & 0xFF:02X}, 0x{(ivhd_entry_len >> 8) & 0xFF:02X}, // Length: {ivhd_entry_len} bytes (只有头部)
        0x{device_id & 0xFF:02X}, 0x{(device_id >> 8) & 0xFF:02X}, // DeviceID: 0x{device_id:04X} (真实)
        0x{capability_offset:02X},                      // Capability Offset: 0x{capability_offset:02X} (真实)
        0x00,                                           // Reserved
        {', '.join(f'0x{b:02X}' for b in iommu_base_address.to_bytes(8, 'little'))}, // IOMMU Base Address: 0x{iommu_base_address:016X} (真实)
        0x{pci_segment & 0xFF:02X}, 0x{(pci_segment >> 8) & 0xFF:02X}, // PCI Segment: 0x{pci_segment:04X} (真实)
        0x{iommu_info & 0xFF:02X}, 0x{(iommu_info >> 8) & 0xFF:02X}, // IOMMU Info: 0x{iommu_info:04X} (真实)
        0x00, 0x00, 0x00, 0x00                          // Reserved

        /* >>> 核心欺骗策略 <<< */
        /* 此IVHD条目长度={ivhd_entry_len}字节，只包含24字节头部 */
        /* 没有任何设备范围条目跟随 = NumberOfDeviceEntries = 0 */
        /* 系统检测：IOMMU存在且已启用，但不保护任何设备 */
        /* 结果：满足IOMMU检测要求，同时确保不卡开机logo */
    }})
}}'''
        return asl_content

    def generate_raw_ivrs(self):
        """生成原生IVRS表的二进制数据 - 核心策略：NumberOfDeviceEntries=0确保不卡开机logo"""
        # 安全地获取参数，确保类型正确
        oem_id = str(self.ivrs_data.get('oem_id', 'AMD')).ljust(6)[:6].encode('ascii')
        oem_table_id = str(self.ivrs_data.get('oem_table_id', 'AmdTable')).ljust(8)[:8].encode('ascii')
        oem_revision = int(self.ivrs_data.get('oem_revision', 1))
        creator_id = str(self.ivrs_data.get('creator_id', 'AMD')).ljust(4)[:4].encode('ascii')
        creator_revision = int(self.ivrs_data.get('creator_revision', 1))

        # 获取真实的IOMMU参数
        iv_info = int(self.ivrs_data.get('iv_info', 0x00203041))
        ivhd_type = int(self.ivrs_data.get('ivhd_type', 0x11))
        ivhd_flags = int(self.ivrs_data.get('ivhd_flags', 0x00))
        device_id = int(self.ivrs_data.get('device_id', 0x0002))
        capability_offset = int(self.ivrs_data.get('capability_offset', 0x40))
        iommu_base_address = int(self.ivrs_data.get('iommu_base_address', 0xFD500000))
        pci_segment = int(self.ivrs_data.get('pci_segment', 0x0000))
        iommu_info = int(self.ivrs_data.get('iommu_info', 0x0000))

        # 计算表长度：ACPI头(36) + IVRS头(12) + IVHD条目(24) = 72字节
        total_table_len = ACPI_HEADER_SIZE + IVRS_HEADER_SIZE + IVHD_HEADER_SIZE

        print(f"✓ 生成原生IVRS表长度: {total_table_len} 字节")
        print(f"✓ 使用真实IOMMU基址: 0x{iommu_base_address:016X}")
        print(f"✓ 核心策略: NumberOfDeviceEntries=0 确保不卡开机logo")

        # 构建IVRS表数据
        ivrs_data = bytearray()

        # ACPI标准头 (36 bytes)
        ivrs_data.extend(b'IVRS')                                    # Signature
        ivrs_data.extend(total_table_len.to_bytes(4, 'little'))      # Length
        ivrs_data.append(0x02)                                       # Revision: 2
        ivrs_data.append(0x00)                                       # Checksum (will be calculated)
        ivrs_data.extend(oem_id)                                     # OEM ID (真实)
        ivrs_data.extend(oem_table_id)                               # OEM Table ID (真实)
        ivrs_data.extend(oem_revision.to_bytes(4, 'little'))         # OEM Revision (真实)
        ivrs_data.extend(creator_id)                                 # Creator ID (真实)
        ivrs_data.extend(creator_revision.to_bytes(4, 'little'))     # Creator Revision (真实)

        # IVRS特殊头 (12 bytes) - 使用增强的参数确保IOMMU被识别
        # 设置更强的IOMMU启用标志：启用所有虚拟化功能
        enhanced_iv_info = iv_info | 0x00000007  # 设置多个启用位
        print(f"✓ 增强IV Info: 0x{iv_info:08X} -> 0x{enhanced_iv_info:08X}")
        ivrs_data.extend(enhanced_iv_info.to_bytes(4, 'little'))     # IV Info (增强)
        ivrs_data.extend(b'\x00' * 8)                                # Reserved

        # IVHD条目 - 核心策略：使用真实硬件参数但NumberOfDeviceEntries=0 (24 bytes)
        ivrs_data.append(ivhd_type)                                  # Type (真实)
        ivrs_data.append(ivhd_flags)                                 # Flags (真实)
        ivrs_data.extend(IVHD_HEADER_SIZE.to_bytes(2, 'little'))     # Length: 24 (只有头部)
        ivrs_data.extend(device_id.to_bytes(2, 'little'))            # Device ID (真实)
        ivrs_data.append(capability_offset)                          # Capability Offset (真实)
        ivrs_data.append(0x00)                                       # Reserved
        ivrs_data.extend(iommu_base_address.to_bytes(8, 'little'))   # IOMMU Base Address (真实)
        ivrs_data.extend(pci_segment.to_bytes(2, 'little'))          # PCI Segment (真实)
        ivrs_data.extend(iommu_info.to_bytes(2, 'little'))           # IOMMU Info (真实)
        ivrs_data.extend(b'\x00' * 4)                                # Reserved

        # 计算校验和
        checksum = (256 - (sum(ivrs_data) % 256)) % 256
        ivrs_data[9] = checksum

        return bytes(ivrs_data)

    def save_raw_ivrs(self, output_path=None):
        """保存原生IVRS二进制文件"""
        if output_path is None:
            output_path = self.rw_file_path.parent / "IVRS_native.aml"
        elif isinstance(output_path, str):
            output_path = Path(output_path)

        ivrs_data = self.generate_raw_ivrs()
        output_path.write_bytes(ivrs_data)
        print(f"✓ 成功生成原生IVRS文件: {output_path}")
        print(f"✓ 文件大小: {len(ivrs_data)} 字节，零设备映射策略")

    def save_asl(self, output_path=None):
        """保存生成的ASL文件"""
        if output_path is None:
            output_path = self.rw_file_path.parent / "ivrs_custom.asl"
        elif isinstance(output_path, str):
            output_path = Path(output_path)
        
        asl_content = self.generate_asl()
        output_path.write_text(asl_content, encoding='utf-8')

def find_ivrs_rw_file():
    """在当前目录查找ivrs.rw文件（不区分大小写）"""
    current_dir = '.'
    possible_names = ['ivrs.rw', 'IVRS.rw', 'Ivrs.rw', 'ivrs.RW', 'IVRS.RW']
    
    for filename in possible_names:
        filepath = os.path.join(current_dir, filename)
        if os.path.exists(filepath):
            print(f"找到输入文件: {filename}")
            return filepath
    
    print("错误：未在当前目录找到 ivrs.rw 文件")
    print("请确保以下文件之一存在：ivrs.rw, IVRS.rw")
    return None

def convert_single_file(input_file, output_file):
    """转换单个文件"""
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        return 1
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print()
    
    try:
        generator = IVRSGenerator(input_file)
        generator.parse_rw_file()
        
        # 生成ASL文件（SSDT格式，兼容性）
        print("正在生成ASL文件（SSDT格式）...")
        generator.save_asl(output_file)

        # 生成原生IVRS表（推荐）
        print("正在生成原生IVRS表...")
        native_output = output_file.replace('.asl', '_native.aml')
        generator.save_raw_ivrs(native_output)

        # 验证文件生成
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✓ 成功生成ASL文件: {output_file} ({file_size} 字节)")

        if os.path.exists(native_output):
            file_size = os.path.getsize(native_output)
            print(f"✓ 成功生成原生IVRS表: {native_output} ({file_size} 字节)")

        print()
        print("🎯 推荐使用方案（原生IVRS表）:")
        print(f"1. 将 {native_output} 重命名为 IVRS.aml")
        print("2. 放入OpenCore的ACPI文件夹")
        print("3. 在config.plist中添加到ACPI->Add部分")
        print("4. 重启后系统将正确识别IOMMU")
        print()
        print("📋 备用方案（SSDT格式）:")
        print(f"1. 编译: iasl {output_file}")
        print("2. 将生成的 .aml 文件放入OpenCore的ACPI文件夹")
        print("3. 在config.plist中添加到ACPI->Add部分")
        print()
        print("⚠️  注意：推荐使用原生IVRS表，系统识别度更高")
        
        return 0
    except Exception as e:
        print(f"错误：处理失败 - {e}")
        return 1

def main():
    """主函数"""
    print("IVRS.rw 到 IVRS.asl 自动转换器 v1.1")
    print("=" * 45)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', '/?']:
            print("用法:")
            print(f"  python {sys.argv[0]} [输入文件] [输出文件]")
            print()
            print("示例:")
            print(f"  python {sys.argv[0]} IVRS.rw IVRS.asl")
            print()
            print("默认:")
            print(f"  python {sys.argv[0]}  # 自动查找当前目录的ivrs.rw -> IVRS.asl")
            return 0
        
        input_file = sys.argv[1]
    else:
        # 自动查找当前目录的ivrs.rw文件
        input_file = find_ivrs_rw_file()
        if input_file is None:
            return 1
    
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    else:
        output_file = "IVRS.asl"
    
    return convert_single_file(input_file, output_file)

if __name__ == "__main__":
    sys.exit(main())