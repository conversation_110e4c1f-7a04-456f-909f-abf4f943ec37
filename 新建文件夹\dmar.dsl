/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembly of dmar.aml
 *
 * ACPI Data Table [DMAR]
 *
 * Format: [HexOffset DecimalOffset ByteLength]  FieldName : FieldValue (in hex)
 */

[000h 0000 004h]                   Signature : "DMAR"    [DMA Remapping Table]
[004h 0004 004h]                Table Length : 0000006E
[008h 0008 001h]                    Revision : 01
[009h 0009 001h]                    Checksum : BB
[00Ah 0010 006h]                      Oem ID : "INTEL "
[010h 0016 008h]                Oem Table ID : "EDK2    "
[018h 0024 004h]                Oem Revision : 00000002
[01Ch 0028 004h]             Asl Compiler ID : "INTL"
[020h 0032 004h]       Asl Compiler Revision : 20250404

[024h 0036 001h]          Host Address Width : 08
[025h 0037 001h]                       Flags : 44
[026h 0038 00Ah]                    Reserved : 4D 41 52 11 44 04 0A 40 44 4D

[030h 0048 002h]               Subtable Type : 5241 [Unknown Subtable Type]
[032h 0050 002h]                      Length : 0040


**** Unknown DMAR subtable type 0x5241


Raw Table Data: Length 110 (0x6E)

    0000: 44 4D 41 52 6E 00 00 00 01 BB 49 4E 54 45 4C 20  // DMARn.....INTEL 
    0010: 45 44 4B 32 20 20 20 20 02 00 00 00 49 4E 54 4C  // EDK2    ....INTL
    0020: 04 04 25 20 08 44 4D 41 52 11 44 04 0A 40 44 4D  // ..% .DMAR.D..@DM
    0030: 41 52 40 00 00 00 01 00 49 4E 54 45 4C 20 45 44  // <EMAIL> ED
    0040: 4B 32 20 20 20 20 02 00 00 00 20 20 20 20 13 00  // K2    ....    ..
    0050: 00 01 26 01 00 00 00 00 00 00 00 00 00 00 00 00  // ..&.............
    0060: 10 00 00 00 00 00 00 10 D9 FE 00 00 00 00        // ..............
