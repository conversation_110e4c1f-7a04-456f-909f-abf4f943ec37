/*
 * 自动生成的DMAR表 - 用于OpenCore VT-d欺骗
 * 生成时间: 2025-07-31 10:54:13
 * 目标: 让系统认为VT-d已启用，但实际不管理任何设备
 * Register Base: 0xFED91000
 * 关键: Flags = 0x00 确保不包含任何PCI设备
 */

DefinitionBlock ("DMAR.aml", "DMAR", 1, "INTEL ", "EDK2    ", 0x00000002)
{
    Name (DMA<PERSON>, Buffer (0x40)  // 64字节 - 最小DMAR表
    {
        /* ACPI表头 (36字节) */
        0x44, 0x4D, 0x41, 0x52,  // "DMAR"
        0x40, 0x00, 0x00, 0x00,  // 长度64字节
        0x01,                    // 版本
        0x00,                    // 校验和(自动计算)
        
        /* OEM信息 */
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20,  // OEM ID "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20,  // OEM Table ID "EDK2    "
        0x02, 0x00, 0x00, 0x00,  // OEM版本
        0x20, 0x20, 0x20, 0x20,  // 创建者ID "    "
        0x13, 0x00, 0x00, 0x01,  // 创建者版本
        
        /* DMAR特定头 (12字节) */
        0x26,                    // 主机地址宽度(38位)
        0x01,                    // 标志(中断重映射)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 保留
        
        /* DRHD结构 - 关键欺骗部分 (16字节) */
        0x00, 0x00,              // 类型(DRHD)
        0x10, 0x00,              // 长度16字节
        0x00,                    // 标志0x00=不包含PCI设备(关键!)
        0x00,                    // 保留
        0x00, 0x00,              // 段号
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00  // 寄存器基址0xFED91000
    })
}