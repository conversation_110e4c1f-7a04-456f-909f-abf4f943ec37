/*
 * 自动生成的DMAR表 - 用于OpenCore VT-d欺骗
 * 生成时间: 2025-07-31 15:35:07
 * 硬件签名: INTEL / EDK2 (默认数据)
 * VT-d寄存器基址: 0x00000000FED91000
 * Host Address Width: 26 bits
 * 核心策略: Flags=0x00 不管理任何设备，确保安全启动
 *
 * 重要说明：
 * - 此表伪装VT-d已启用，满足系统检测要求
 * - Flags=0x00确保不会卡在开机logo
 * - 使用真实硬件参数提高兼容性
 */

DefinitionBlock ("", "SSDT", 2, "INTEL ", "DMRTBL", 0x00000001)
{
    /*
     * DMAR Raw Data Table - ACPI compliant structure
     * Total length: 64 bytes (0x40)
     * Creator ID: "    " (0x01000013)
     *
     * 核心策略说明：
     * 1. 使用真实硬件参数提高兼容性
     * 2. DRHD条目长度=16字节，只包含头部
     * 3. Flags=0x00，确保不卡开机logo
     * 4. 系统检测到VT-d存在但不管理任何设备
     */
    Name (DMAR, Buffer (0x40)
    {
        /* ACPI Standard Header (36 bytes) */
        0x44, 0x4D, 0x41, 0x52,                         // Signature: "DMAR"
        0x40, 0x00, 0x00, 0x00, // Length: 64 bytes
        0x01,                               // Revision: 1
        0x00,                                           // Checksum (calculated by BIOS)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20, // OEM ID: "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20, // OEM Table ID: "EDK2    "
        0x02, 0x00, 0x00, 0x00, // OEM Revision: 0x00000002
        0x20, 0x20, 0x20, 0x20, // Creator ID: "    "
        0x13, 0x00, 0x00, 0x01, // Creator Revision: 0x01000013

        /* DMAR-specific Header (12 bytes) - 使用真实参数 */
        0x1A,                             // Host Address Width: 26 bits (真实)
        0x01,                                           // Flags: Interrupt Remapping (真实)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Reserved fields

        /* DRHD Entry - 使用真实硬件参数但确保Flags=0x00 */
        0x00, 0x00,                                     // Type: DRHD (DMA Remapping Hardware Unit Definition)
        0x10, 0x00, // Length: 16 bytes (只有头部)
        0x00,                             // Flags: 0x00 (核心：确保不卡开机logo)
        0x00,                                           // Reserved
        0x00, 0x00, // Segment Number: 0x0000 (真实)
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00 // Register Base Address: 0x00000000FED91000 (真实)

        /* >>> 核心VT-d欺骗策略 <<< */
        /* 此DRHD条目长度=16字节，只包含16字节头部 */
        /* 没有任何设备范围条目跟随 = 不管理任何设备 */
        /* 系统检测：VT-d存在且已启用，但不保护任何设备 */
        /* 结果：满足VT-d检测要求，同时确保不卡开机logo */
    })
}