/*
 * Auto-generated DMAR Table for VT-d Testing
 * Generated: 2025-07-31 10:48:57
 * Source: DMAR.rw
 *
 * Strategy: Empty device mapping - DMAR exists but protects nothing.
 * This file uses real OEM signatures for authenticity and a safe
 * hardware definition to ensure system stability.
 *
 * IMPORTANT: This generates a raw DMAR data table, not ASL code.
 * The data structure directly matches ACPI DMAR specification.
 */

DefinitionBlock ("", "SSDT", 2, "INTEL ", "DMRTBL", 0x00000001)
{
    /*
     * DMAR Raw Data Table - ACPI compliant structure
     * Total length: 64 bytes (0x40)
     * Creator ID: "    " (0x01000013)
     * Host Address Width: 26 bits (0x1A)
     */
    Name (DMAR, Buffer (0x40)
    {
        /* ACPI Standard Header (36 bytes) */
        0x44, 0x4D, 0x41, 0x52,                         // Signature: "DMAR"
        0x40, 0x00, 0x00, 0x00, // Length: 64
        0x01,                               // Revision: 1
        0x00,                                           // Checksum (calculated by BIOS)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20, // OEM ID: "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20, // OEM Table ID: "EDK2    "
        0x02, 0x00, 0x00, 0x00, // OEM Revision: 0x00000002
        0x20, 0x20, 0x20, 0x20, // Creator ID: "    "
        0x13, 0x00, 0x00, 0x01, // Creator Revision: 0x01000013

        /* DMAR-specific Header (12 bytes) */
        0x1A,                             // Host Address Width: 26 bits (真实值)
        0x01,                                           // Flags: Interrupt Remapping (真实值)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Reserved fields

        /* DRHD Entry - Minimal VT-d unit definition (16 bytes) */
        0x00, 0x00,                                     // Type: DRHD (DMA Remapping Hardware Unit Definition)
        0x10, 0x00, // Length: 16 bytes
        0x01,                                           // Flags: 0x01 = INCLUDE_PCI_ALL (让Windows认为DMA保护开启)
        0x00,                                           // Reserved
        0x00, 0x00,                                     // Segment Number: 0x0000 (安全策略)
        
        /* >>> THE CORE OF THE VT-d SPOOF <<< */
        /* Register Base Address using real hardware value */
        0x00,
        0x10,
        0xD9,
        0xFE,
        0x00,
        0x00,
        0x00,
        0x00  // Real Register Base: 0x00000000FED91000
        
        /* NO Device Scope Entries follow - this means NO devices are managed */
        /* The DRHD declares a VT-d unit exists but manages ZERO devices */
        /* This creates a "phantom VT-d" for testing purposes */
    })
    
    /*  
     * Device _STA method to indicate DMAR presence
     */
    Method (_STA, 0, NotSerialized)
    {
        Return (0x0F)  // Present, Enabled, Show in UI, Functioning
    }
}