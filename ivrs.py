#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IVRS ASL Generator - 从IVRS.rw真实数据生成定制的ivrs_custom.asl
基于真实硬件数据，但使用安全的“完整硬件仿真”策略。
(NumberOfDeviceEntries = 0)
"""

import re
from datetime import datetime
from pathlib import Path

class IVRSGenerator:
    """
    解析IVRS.rw文件并生成一个安全的、用于仿真的ivrs_custom.asl文件。
    """
    def __init__(self, rw_file_path):
        self.rw_file_path = Path(rw_file_path)
        if not self.rw_file_path.exists():
            raise FileNotFoundError(f"Error: Input file not found at {rw_file_path}")
        self.ivrs_data = {}

    def parse_rw_file(self):
        """解析IVRS.rw文件，提取关键硬件签名信息。"""
        content = self.rw_file_path.read_text(encoding='utf-8', errors='ignore')
        
        patterns = {
            'oem_id': r'OEM ID\s+"([^"]+)"',
            'oem_table_id': r'OEM Table ID\s+"([^"]+)"',
            'oem_revision': r'OEM Revision\s+0x([0-9A-F]+)',
            'creator_id': r'Creator ID\s+"([^"]*)"',
            'creator_revision': r'Creator Revision\s+0x([0-9A-F]+)',
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                self.ivrs_data[key] = value if 'id' in key else int(value, 16)
        
        # 为缺失的值提供安全的默认值
        if 'oem_id' not in self.ivrs_data: self.ivrs_data['oem_id'] = "AMD"
        if 'oem_table_id' not in self.ivrs_data: self.ivrs_data['oem_table_id'] = "AmdTable"
        if 'oem_revision' not in self.ivrs_data: self.ivrs_data['oem_revision'] = 1
        if 'creator_id' not in self.ivrs_data: self.ivrs_data['creator_id'] = "AMD"
        if 'creator_revision' not in self.ivrs_data: self.ivrs_data['creator_revision'] = 1


    def generate_asl(self):
        """根据提取的数据和安全策略，生成ASL文件内容。"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 使用从文件中提取的真实数据，如果不存在则使用默认值
        oem_id = self.ivrs_data.get('oem_id').ljust(6)[:6]
        oem_table_id = self.ivrs_data.get('oem_table_id').ljust(8)[:8]
        oem_revision = self.ivrs_data.get('oem_revision')
        creator_id = self.ivrs_data.get('creator_id').ljust(4)[:4]
        creator_revision = self.ivrs_data.get('creator_revision')

        asl_content = f'''/*
 * Auto-generated IVRS Table for IOMMU Spoofing
 * Generated: {timestamp}
 * Source: {self.rw_file_path.name}
 *
 * Strategy: Full hardware simulation with zero device entries.
 * This file uses real OEM signatures for authenticity and a safe
 * hardware definition to ensure system stability.
 */
DefinitionBlock (
    "IVRS.aml",                 // Output Filename
    "IVRS",                     // Signature
    2,                          // Revision
    "{oem_id}",                 // OEM ID (from real data)
    "{oem_table_id}",           // OEM Table ID (from real data)
    0x{oem_revision:08X}        // OEM Revision (from real data)
)
{{
    /*
     * IVRS Table Data - Compatible ASL Structure
     * Using Name + Buffer for maximum iasl compiler compatibility.
     * Creator ID "{creator_id}" and Revision 0x{creator_revision:08X} are from real hardware data.
     */
    Name (IVDT, Buffer (48)
    {{
        // IVRS-specific Header (12 Bytes)
        0x00, 0x00, 0x00, 0x00,                         // IOMMU Info
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Reserved

        // IVHD (IOMMU Hardware Definition) Entry - Type 11h (36 Bytes total)
        0x11,                                           // Type: IVHD Type 11h
        0x00,                                           // Flags: No special flags  
        0x24, 0x00,                                     // Length: 36 bytes (0x0024)
        0x00, 0x00,                                     // Device ID: Generic (0x0000)
        0x18,                                           // Capability Offset: 0x18
        0x00,                                           // Reserved
        0x00, 0x00, 0xD8, 0xFE, 0x00, 0x00, 0x00, 0x00, // IOMMU Base Address: 0xFED80000
        0x00, 0x00,                                     // PCI Segment Group: 0
        0x00, 0x00,                                     // IOMMU Info: Generic
        0x00, 0x00, 0x00, 0x00,                         // Reserved fields
        
        /* >>> THE CORE OF THE SPOOF <<< */
        // NO Device Range Entries follow - this means NumberOfDeviceEntries = 0
        // The IVHD length (36 bytes) includes only the header, no device entries
        // This creates a "phantom IOMMU" that appears present but protects nothing
        
        // Additional padding to ensure proper buffer size
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    }})
    
    /*  
     * Device _STA method to indicate IVRS presence
     */
    Method (_STA, 0, NotSerialized)
    {{
        Return (0x0F)  // Present, Enabled, Show in UI, Functioning
    }}
}}'''
        return asl_content

    def save_asl(self, output_path=None):
        """保存生成的ASL文件"""
        if output_path is None:
            output_path = self.rw_file_path.parent / "ivrs_custom.asl"
        
        asl_content = self.generate_asl()
        output_path.write_text(asl_content, encoding='utf-8')
        print(f"[OK] Successfully generated custom ASL file: {output_path}")
        print("[OK] It combines real hardware signatures with a safe spoofing strategy.")

def main():
    """主函数，处理命令行调用"""
    import sys
    
    if len(sys.argv) != 2:
        print(f"Usage: python {sys.argv[0]} <IVRS.rw_filepath>")
        sys.exit(1)
        
    rw_file = sys.argv[1]
    
    try:
        generator = IVRSGenerator(rw_file)
        generator.parse_rw_file()
        generator.save_asl()
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()