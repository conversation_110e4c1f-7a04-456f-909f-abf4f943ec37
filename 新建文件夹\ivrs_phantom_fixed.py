#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IVRS Phantom IOMMU Generator - "幻影IOMMU"生成器
从IVRS.rw真实数据生成一个"存在但不可用"的IVRS表。
核心策略：IOMMU Base Address = 0
"""

import re
import sys
import os
import struct  # 添加缺少的导入
from datetime import datetime
from pathlib import Path

# ACPI表结构常量
ACPI_HEADER_SIZE = 36
IVRS_HEADER_SIZE = 12
IVHD_HEADER_SIZE = 24

class IVRSPhantomGenerator:
    def __init__(self, rw_file_path):
        self.rw_file_path = Path(rw_file_path)
        if not self.rw_file_path.exists():
            raise FileNotFoundError(f"Error: Input file not found at {rw_file_path}")
        self.ivrs_data = {}

    def parse_rw_file(self):
        """解析IVRS.rw文件，提取关键硬件签名信息和IOMMU参数。"""
        content = self.rw_file_path.read_bytes().decode('latin1')
        print(f"正在解析文件: {self.rw_file_path}")

        # 基本表头信息提取
        basic_patterns = {
            'oem_id': r'OEM ID\s+"([^"]+)"',
            'oem_table_id': r'OEM Table ID\s+"([^"]+)"',
            'oem_revision': r'OEM Revision\s+0x([0-9A-F]+)',
            'creator_id': r'Creator ID\s+"([^"]*)"',
            'creator_revision': r'Creator Revision\s+0x([0-9A-F]+)',
        }
        for key, pattern in basic_patterns.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                self.ivrs_data[key] = value if 'id' in key else int(value, 16)

        # 提取真实IOMMU硬件参数
        self._extract_real_iommu_params(content)
        self._display_final_params()
        
    def _extract_real_iommu_params(self, content):
        """从IVRS.rw原始数据中提取真实的IOMMU硬件参数"""
        print("正在提取真实IOMMU硬件参数...")
        hex_lines = re.findall(r'^[0-9A-Fa-f]{4}:((?:\s[0-9A-Fa-f]{2})+)', content, re.MULTILINE)
        if not hex_lines: # Fallback for "Data	0x..." format
            hex_lines = re.findall(r'^Data\s+((?:0x[0-9A-Fa-f]{2}\s*)+)', content, re.MULTILINE)
            hex_data = "".join(h.replace('0x', '') for h in "".join(hex_lines).split())
        else:
            hex_data = "".join("".join(line.split()) for line in hex_lines)

        if not hex_data:
            print("[WARN] 未能在.rw文件中找到十六进制数据块。")
            return

        binary_data = bytes.fromhex(hex_data)
        
        # IVRS特定头信息 (ACPI头后的前12字节)
        if len(binary_data) > 36:
            iv_info = struct.unpack_from('<I', binary_data, 36)[0]
            self.ivrs_data['iv_info'] = iv_info
        
        # 寻找第一个IVHD条目
        offset = ACPI_HEADER_SIZE + IVRS_HEADER_SIZE
        if len(binary_data) > offset + 24:  # 确保有足够的数据
            ivhd_type = binary_data[offset]
            if ivhd_type in [0x10, 0x11]:
                ivhd = struct.unpack_from('<BBHHIQHH', binary_data, offset)
                self.ivrs_data.update({
                    'ivhd_type': ivhd[0],
                    'ivhd_flags': ivhd[1],
                    'device_id': ivhd[3],
                    'capability_offset': ivhd[4],
                    'iommu_base_address': ivhd[5],
                    'pci_segment': ivhd[6],
                    'iommu_info': ivhd[7]
                })
                print(f"✓ 采集到真实IOMMU参数，基地址为: 0x{ivhd[5]:016X}")

    def _display_final_params(self):
        """显示最终使用的参数"""
        print("\n=== 最终使用的IVRS参数（修改前）===")
        # Display params before modification
        for key, value in self.ivrs_data.items():
            if isinstance(value, int):
                print(f"{key}: 0x{value:X}")
            else:
                print(f"{key}: '{value}'")
        print("=" * 35)

    def generate_raw_ivrs(self):
        """生成原生IVRS表的二进制数据"""
        # 使用安全的默认值或解析出的真实值
        oem_id = str(self.ivrs_data.get('oem_id', 'AMD')).ljust(6)[:6].encode('ascii')
        oem_table_id = str(self.ivrs_data.get('oem_table_id', 'AmdTable')).ljust(8)[:8].encode('ascii')
        oem_revision = int(self.ivrs_data.get('oem_revision', 1))
        creator_id = str(self.ivrs_data.get('creator_id', 'AMD')).ljust(4)[:4].encode('ascii')
        creator_revision = int(self.ivrs_data.get('creator_revision', 1))
        iv_info = int(self.ivrs_data.get('iv_info', 0))
        ivhd_type = int(self.ivrs_data.get('ivhd_type', 0x11))
        ivhd_flags = int(self.ivrs_data.get('ivhd_flags', 0x00))
        device_id = int(self.ivrs_data.get('device_id', 0x0000))
        capability_offset = int(self.ivrs_data.get('capability_offset', 0x18))
        iommu_base_address = int(self.ivrs_data.get('iommu_base_address', 0))
        pci_segment = int(self.ivrs_data.get('pci_segment', 0x0000))
        iommu_info = int(self.ivrs_data.get('iommu_info', 0x0000))
        
        # >>> 核心"幻影"策略：强制基地址为零 <<<
        print("\n[STRATEGY] 应用\"幻影IOMMU\"策略...")
        print(f"[CHANGE] IOMMU Base Address: 0x{iommu_base_address:016X} -> 0x0000000000000000")
        iommu_base_address = 0

        # 计算表长度：ACPI头(36) + IVRS头(12) + IVHD条目(24) = 72字节
        total_table_len = ACPI_HEADER_SIZE + IVRS_HEADER_SIZE + IVHD_HEADER_SIZE

        # 构建IVRS表数据
        ivrs_data = bytearray(total_table_len)

        # ACPI标准头 (36 bytes)
        struct.pack_into('<4sI B B 6s 8s I 4s I', ivrs_data, 0,
                         b'IVRS', total_table_len, 2, 0,
                         oem_id, oem_table_id, oem_revision,
                         creator_id, creator_revision)

        # IVRS特殊头 (12 bytes)
        struct.pack_into('<I 8x', ivrs_data, ACPI_HEADER_SIZE, iv_info)

        # IVHD条目 (24 bytes)
        struct.pack_into('<B B H H B B Q H H 4x', ivrs_data, ACPI_HEADER_SIZE + IVRS_HEADER_SIZE,
                         ivhd_type, ivhd_flags, IVHD_HEADER_SIZE,
                         device_id, capability_offset, 0, # Reserved byte
                         iommu_base_address, # The spoofed 0 address
                         pci_segment, iommu_info)
        
        # 计算校验和
        checksum = (256 - (sum(ivrs_data) % 256)) % 256
        ivrs_data[9] = checksum

        return bytes(ivrs_data)

    def save_raw_ivrs(self, output_path=None):
        """保存原生IVRS二进制文件"""
        if output_path is None:
            output_path = self.rw_file_path.parent / "IVRS_Phantom.aml"
        
        ivrs_data = self.generate_raw_ivrs()
        output_path.write_bytes(ivrs_data)
        print(f"\n✓ 成功生成\"幻影\"IVRS文件: {output_path}")
        print(f"✓ 文件大小: {len(ivrs_data)} 字节")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print(f"Usage: python {sys.argv[0]} <IVRS.rw_filepath>")
        sys.exit(1)
    rw_file = sys.argv[1]
    
    try:
        generator = IVRSPhantomGenerator(rw_file)
        generator.parse_rw_file()
        generator.save_raw_ivrs()
    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
