/*
 * 自动生成的IVRS表 - 用于OpenCore AMD IOMMU欺骗
 * 生成时间: 2025-07-31 20:02:23
 * 硬件签名: AMD / AmdTable (真实数据)
 * IOMMU基址: 0x00000000FD300000
 * IV Info: 0x00203041
 * 核心策略: NumberOfDeviceEntries=0 不管理任何设备，确保安全启动
 *
 * 重要说明：
 * - 此表伪装IOMMU已启用，满足系统检测要求
 * - NumberOfDeviceEntries=0确保不会卡在开机logo
 * - 使用真实硬件参数提高兼容性
 */

DefinitionBlock ("", "SSDT", 2, "AMD   ", "IVRSTBL", 0x00000001)
{
    /*
     * IVRS Raw Data Table - ACPI compliant structure
     * Total length: 72 bytes (0x48)
     * Creator ID: "AMD " (0x00000001)
     */
    Name (IVRS, Buffer (0x48)
    {
        /* ACPI Standard Header (36 bytes) */
        0x49, 0x56, 0x52, 0x53,                         // Signature: "IVRS"
        0x48, 0x00, 0x00, 0x00, // Length: 72
        0x02,                                           // Revision: 2
        0x00,                                           // Checksum (calculated by BIOS)
        0x41, 0x4D, 0x44, 0x20, 0x20, 0x20, // OEM ID: "AMD   "
        0x41, 0x6D, 0x64, 0x54, 0x61, 0x62, 0x6C, 0x65, // OEM Table ID: "AmdTable"
        0x01, 0x00, 0x00, 0x00, // OEM Revision: 0x00000001
        0x41, 0x4D, 0x44, 0x20, // Creator ID: "AMD "
        0x01, 0x00, 0x00, 0x00, // Creator Revision: 0x00000001

        /* IVRS-specific Header (12 bytes) - 使用真实IV Info */
        0x41, 0x30, 0x20, 0x00, // IV Info: 0x00203041 (真实)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Reserved fields

        /* IVHD Entry - 使用真实硬件参数但确保NumberOfDeviceEntries=0 */
        0x10,                              // Type: 0x10 (真实IVHD类型)
        0xB0,                             // Flags: 0xB0 (真实标志)
        0x18, 0x00, // Length: 24 bytes (只有头部)
        0x00, 0x02, // DeviceID: 0x0200 (真实)
        0x40,                      // Capability Offset: 0x40 (真实)
        0x00,                                           // Reserved
        0x00, 0x00, 0x30, 0xFD, 0x00, 0x00, 0x00, 0x00, // IOMMU Base Address: 0x00000000FD300000 (真实)
        0x00, 0x00, // PCI Segment: 0x0000 (真实)
        0x00, 0x00, // IOMMU Info: 0x0000 (真实)
        0x00, 0x00, 0x00, 0x00                          // Reserved

        /* >>> 核心欺骗策略 <<< */
        /* 此IVHD条目长度=24字节，只包含24字节头部 */
        /* 没有任何设备范围条目跟随 = NumberOfDeviceEntries = 0 */
        /* 系统检测：IOMMU存在且已启用，但不保护任何设备 */
        /* 结果：满足IOMMU检测要求，同时确保不卡开机logo */
    })
}