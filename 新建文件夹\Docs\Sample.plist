<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>#WARNING - 1</key>
	<string>This is just a sample. Do NOT try loading it.</string>
	<key>#WARNING - 2</key>
	<string>Ensure you understand EVERY field before booting.</string>
	<key>#WARNING - 3</key>
	<string>In most cases recommended to use Sample.plist</string>
	<key>#WARNING - 4</key>
	<string>Use SampleCustom.plist only for special cases.</string>
	<key>ACPI</key>
	<dict>
		<key>Add</key>
		<array>
			<dict>
				<key>Comment</key>
				<string>My custom DSDT</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>DSDT.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>My custom SSDT</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-1.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-ALS0.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-AWAC-DISABLE.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-BRG0.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-EC-USBX.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-EC.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-EHCx-DISABLE.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-IMEI.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-PLUG.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-PMC.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-PNLF.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-RTC0-RANGE.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-RTC0.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-SBUS-MCHC.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Read the comment in dsl sample</string>
				<key>Enabled</key>
				<false/>
				<key>Path</key>
				<string>SSDT-UNC.aml</string>
			</dict>
		</array>
		<key>Delete</key>
		<array>
			<dict>
				<key>All</key>
				<false/>
				<key>Comment</key>
				<string>Delete CpuPm</string>
				<key>Enabled</key>
				<false/>
				<key>OemTableId</key>
				<data>Q3B1UG0AAAA=</data>
				<key>TableLength</key>
				<integer>0</integer>
				<key>TableSignature</key>
				<data>U1NEVA==</data>
			</dict>
			<dict>
				<key>All</key>
				<false/>
				<key>Comment</key>
				<string>Delete Cpu0Ist</string>
				<key>Enabled</key>
				<false/>
				<key>OemTableId</key>
				<data>Q3B1MElzdAA=</data>
				<key>TableLength</key>
				<integer>0</integer>
				<key>TableSignature</key>
				<data>U1NEVA==</data>
			</dict>
		</array>
		<key>Patch</key>
		<array>
			<dict>
				<key>Base</key>
				<string></string>
				<key>BaseSkip</key>
				<integer>0</integer>
				<key>Comment</key>
				<string>Replace one byte sequence with another</string>
				<key>Count</key>
				<integer>0</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data>ESIzRA==</data>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>OemTableId</key>
				<data></data>
				<key>Replace</key>
				<data>RDMiEQ==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
				<key>TableLength</key>
				<integer>0</integer>
				<key>TableSignature</key>
				<data></data>
			</dict>
			<dict>
				<key>Base</key>
				<string>\_SB.PCI0.LPCB.HPET</string>
				<key>BaseSkip</key>
				<integer>0</integer>
				<key>Comment</key>
				<string>HPET _CRS to XCRS</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data>X0NSUw==</data>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>OemTableId</key>
				<data></data>
				<key>Replace</key>
				<data>WENSUw==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
				<key>TableLength</key>
				<integer>0</integer>
				<key>TableSignature</key>
				<data></data>
			</dict>
		</array>
		<key>Quirks</key>
		<dict>
			<key>FadtEnableReset</key>
			<false/>
			<key>NormalizeHeaders</key>
			<false/>
			<key>RebaseRegions</key>
			<false/>
			<key>ResetHwSig</key>
			<false/>
			<key>ResetLogoStatus</key>
			<true/>
			<key>SyncTableIds</key>
			<false/>
		</dict>
	</dict>
	<key>Booter</key>
	<dict>
		<key>MmioWhitelist</key>
		<array>
			<dict>
				<key>Address</key>
				<integer>4275159040</integer>
				<key>Comment</key>
				<string>Haswell: SB_RCBA is a 0x4 page memory region, containing SPI_BASE at 0x3800 (SPI_BASE_ADDRESS)</string>
				<key>Enabled</key>
				<false/>
			</dict>
			<dict>
				<key>Address</key>
				<integer>4278190080</integer>
				<key>Comment</key>
				<string>Generic: PCI root is a 0x1000 page memory region used by some types of firmware</string>
				<key>Enabled</key>
				<false/>
			</dict>
		</array>
		<key>Patch</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Comment</key>
				<string>macOS to hacOS</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data>bWFjT1M=</data>
				<key>Identifier</key>
				<string>Apple</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>Replace</key>
				<data>aGFjT1M=</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
		</array>
		<key>Quirks</key>
		<dict>
			<key>AllowRelocationBlock</key>
			<false/>
			<key>AvoidRuntimeDefrag</key>
			<true/>
			<key>ClearTaskSwitchBit</key>
			<false/>
			<key>DevirtualiseMmio</key>
			<false/>
			<key>DisableSingleUser</key>
			<false/>
			<key>DisableVariableWrite</key>
			<false/>
			<key>DiscardHibernateMap</key>
			<false/>
			<key>EnableSafeModeSlide</key>
			<true/>
			<key>EnableWriteUnprotector</key>
			<true/>
			<key>FixupAppleEfiImages</key>
			<true/>
			<key>ForceBooterSignature</key>
			<false/>
			<key>ForceExitBootServices</key>
			<false/>
			<key>ProtectMemoryRegions</key>
			<false/>
			<key>ProtectSecureBoot</key>
			<false/>
			<key>ProtectUefiServices</key>
			<false/>
			<key>ProvideCustomSlide</key>
			<true/>
			<key>ProvideMaxSlide</key>
			<integer>0</integer>
			<key>RebuildAppleMemoryMap</key>
			<false/>
			<key>ResizeAppleGpuBars</key>
			<integer>-1</integer>
			<key>SetupVirtualMap</key>
			<true/>
			<key>SignalAppleOS</key>
			<false/>
			<key>SyncRuntimePermissions</key>
			<false/>
		</dict>
	</dict>
	<key>DeviceProperties</key>
	<dict>
		<key>Add</key>
		<dict>
			<key>PciRoot(0x0)/Pci(0x1b,0x0)</key>
			<dict>
				<key>layout-id</key>
				<data>AQAAAA==</data>
			</dict>
		</dict>
		<key>Delete</key>
		<dict/>
	</dict>
	<key>Kernel</key>
	<dict>
		<key>Add</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>Lilu.kext</string>
				<key>Comment</key>
				<string>Patch engine</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/Lilu</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>8.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>VirtualSMC.kext</string>
				<key>Comment</key>
				<string>SMC emulator</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VirtualSMC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>8.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>WhateverGreen.kext</string>
				<key>Comment</key>
				<string>Video patches</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/WhateverGreen</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>10.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>AppleALC.kext</string>
				<key>Comment</key>
				<string>Audio patches</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/AppleALC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>8.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>IntelMausi.kext</string>
				<key>Comment</key>
				<string>Intel Ethernet LAN</string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IntelMausi</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>13.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>Legacy_USB3.kext</string>
				<key>Comment</key>
				<string>XHC ports configuration</string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string></string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>15.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>AppleMCEReporterDisabler.kext</string>
				<key>Comment</key>
				<string>AppleMCEReporter disabler</string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string></string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>19.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>VoodooPS2Controller.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VoodooPS2Controller</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>15.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>VoodooPS2Controller.kext/Contents/PlugIns/VoodooPS2Keyboard.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VoodooPS2Keyboard</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>15.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>VoodooPS2Controller.kext/Contents/PlugIns/VoodooPS2Mouse.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VoodooPS2Mouse</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>15.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>VoodooPS2Controller.kext/Contents/PlugIns/VoodooPS2Trackpad.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VoodooPS2Trackpad</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>15.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>VoodooPS2Controller.kext/Contents/PlugIns/VoodooInput.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VoodooInput</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>15.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>AirportBrcmFixup.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/AirportBrcmFixup</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>12.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>AirportBrcmFixup.kext/Contents/PlugIns/AirPortBrcm4360_Injector.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string></string>
				<key>MaxKernel</key>
				<string>19.9.9</string>
				<key>MinKernel</key>
				<string>12.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>AirportBrcmFixup.kext/Contents/PlugIns/AirPortBrcmNIC_Injector.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string></string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>17.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>IOSkywalkFamily.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IOSkywalkFamily</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>23.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>IO80211FamilyLegacy.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IO80211FamilyLegacy</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>23.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>BundlePath</key>
				<string>IO80211FamilyLegacy.kext/Contents/PlugIns/AirPortBrcmNIC.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/AirPortBrcmNIC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>23.0.0</string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
		</array>
		<key>Block</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>Identifier</key>
				<string>com.apple.driver.AppleTyMCEDriver</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Strategy</key>
				<string>Disable</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>Identifier</key>
				<string>com.apple.iokit.IOSkywalkFamily</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>23.0.0</string>
				<key>Strategy</key>
				<string>Exclude</string>
			</dict>
		</array>
		<key>Emulate</key>
		<dict>
			<key>Cpuid1Data</key>
			<data></data>
			<key>Cpuid1Mask</key>
			<data></data>
			<key>DummyPowerManagement</key>
			<false/>
			<key>MaxKernel</key>
			<string></string>
			<key>MinKernel</key>
			<string></string>
		</dict>
		<key>Force</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>System/Library/Extensions/IONetworkingFamily.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IONetworkingFamily</string>
				<key>Identifier</key>
				<string>com.apple.iokit.IONetworkingFamily</string>
				<key>MaxKernel</key>
				<string>13.99.99</string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
		</array>
		<key>Patch</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Base</key>
				<string>__ZN8AppleRTC18setupDateTimeAlarmEPK11RTCDateTime</string>
				<key>Comment</key>
				<string>Disable RTC wake scheduling</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>com.apple.driver.AppleRTC</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string>19.0.0</string>
				<key>Replace</key>
				<data>ww==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Base</key>
				<string>_AcpiOsVprintf</string>
				<key>Comment</key>
				<string>Disable ACPI logging</string>
				<key>Count</key>
				<integer>0</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>com.apple.driver.AppleACPIPlatform</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string>18.5.0</string>
				<key>MinKernel</key>
				<string>18.5.0</string>
				<key>Replace</key>
				<data>ww==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Base</key>
				<string>__ZN11BCM5701Enet14getAdapterInfoEv</string>
				<key>Comment</key>
				<string>Broadcom BCM57785 patch</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data>6AAA//9miYMABQAA</data>
				<key>Identifier</key>
				<string>com.apple.iokit.AppleBCM5701Ethernet</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data>/wAA////////////</data>
				<key>MaxKernel</key>
				<string>19.9.9</string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>uLQWAABmiYMABQAA</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Base</key>
				<string>__ZN24IOBluetoothHCIController5startEP9IOService</string>
				<key>Comment</key>
				<string>Disable Bluetooth entirely</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>com.apple.iokit.IOBluetoothFamily</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>ww==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Base</key>
				<string>_panic</string>
				<key>Comment</key>
				<string>Send panic string to serial port</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>MfaKD4TJdQT/xrEKZrr9A+yoIHT7Zrr4A4jI7kj/x4X2dN/r/g==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Base</key>
				<string>_kernel_debug_string_early</string>
				<key>Comment</key>
				<string>Send early prints to serial port</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>MfaKD4TJdQT/xrEKZrr9A+yoIHT7Zrr4A4jI7kj/x4X2dN/D</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Base</key>
				<string>_disable_serial_output</string>
				<key>Comment</key>
				<string>Enable early serial output on RELEASE kernel</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>AA==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Base</key>
				<string>_vstart</string>
				<key>Comment</key>
				<string>Print K and dead loop</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>sUtmuv0D7KggdPtmuvgDiMjusQpmuv0D7KggdPtmuvgDiMju6/4=</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>Base</key>
				<string>_vstart</string>
				<key>Comment</key>
				<string>Early reboot</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data></data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>0</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>Replace</key>
				<data>McCwBrr5DAAA7uv+</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Base</key>
				<string>_early_random</string>
				<key>Comment</key>
				<string>SurPlus v1 - PART 1 of 2 - Patch read_erandom (inlined in _early_random)</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data>AHQjSIs=</data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>800</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string>21.1.0</string>
				<key>MinKernel</key>
				<string>20.4.0</string>
				<key>Replace</key>
				<data>AOsjSIs=</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>Arch</key>
				<string>x86_64</string>
				<key>Base</key>
				<string>_register_and_init_prng</string>
				<key>Comment</key>
				<string>SurPlus v1 - PART 2 of 2 - Patch register_and_init_prng</string>
				<key>Count</key>
				<integer>1</integer>
				<key>Enabled</key>
				<false/>
				<key>Find</key>
				<data>ukgBAAAx9g==</data>
				<key>Identifier</key>
				<string>kernel</string>
				<key>Limit</key>
				<integer>256</integer>
				<key>Mask</key>
				<data></data>
				<key>MaxKernel</key>
				<string>21.1.0</string>
				<key>MinKernel</key>
				<string>20.4.0</string>
				<key>Replace</key>
				<data>ukgBAADrBQ==</data>
				<key>ReplaceMask</key>
				<data></data>
				<key>Skip</key>
				<integer>0</integer>
			</dict>
		</array>
		<key>Quirks</key>
		<dict>
			<key>AppleCpuPmCfgLock</key>
			<false/>
			<key>AppleXcpmCfgLock</key>
			<false/>
			<key>AppleXcpmExtraMsrs</key>
			<false/>
			<key>AppleXcpmForceBoost</key>
			<false/>
			<key>CustomPciSerialDevice</key>
			<false/>
			<key>CustomSMBIOSGuid</key>
			<false/>
			<key>DisableIoMapper</key>
			<false/>
			<key>DisableIoMapperMapping</key>
			<false/>
			<key>DisableLinkeditJettison</key>
			<true/>
			<key>DisableRtcChecksum</key>
			<false/>
			<key>ExtendBTFeatureFlags</key>
			<false/>
			<key>ExternalDiskIcons</key>
			<false/>
			<key>ForceAquantiaEthernet</key>
			<false/>
			<key>ForceSecureBootScheme</key>
			<false/>
			<key>IncreasePciBarSize</key>
			<false/>
			<key>LapicKernelPanic</key>
			<false/>
			<key>LegacyCommpage</key>
			<false/>
			<key>PanicNoKextDump</key>
			<false/>
			<key>PowerTimeoutKernelPanic</key>
			<false/>
			<key>ProvideCurrentCpuInfo</key>
			<false/>
			<key>SetApfsTrimTimeout</key>
			<integer>-1</integer>
			<key>ThirdPartyDrives</key>
			<false/>
			<key>XhciPortLimit</key>
			<false/>
		</dict>
		<key>Scheme</key>
		<dict>
			<key>CustomKernel</key>
			<false/>
			<key>FuzzyMatch</key>
			<true/>
			<key>KernelArch</key>
			<string>Auto</string>
			<key>KernelCache</key>
			<string>Auto</string>
		</dict>
	</dict>
	<key>Misc</key>
	<dict>
		<key>BlessOverride</key>
		<array/>
		<key>Boot</key>
		<dict>
			<key>ConsoleAttributes</key>
			<integer>0</integer>
			<key>HibernateMode</key>
			<string>None</string>
			<key>HibernateSkipsPicker</key>
			<false/>
			<key>HideAuxiliary</key>
			<true/>
			<key>InstanceIdentifier</key>
			<string></string>
			<key>LauncherOption</key>
			<string>Disabled</string>
			<key>LauncherPath</key>
			<string>Default</string>
			<key>PickerAttributes</key>
			<integer>17</integer>
			<key>PickerAudioAssist</key>
			<false/>
			<key>PickerMode</key>
			<string>Builtin</string>
			<key>PickerVariant</key>
			<string>Auto</string>
			<key>PollAppleHotKeys</key>
			<false/>
			<key>ShowPicker</key>
			<true/>
			<key>TakeoffDelay</key>
			<integer>0</integer>
			<key>Timeout</key>
			<integer>5</integer>
		</dict>
		<key>Debug</key>
		<dict>
			<key>AppleDebug</key>
			<false/>
			<key>ApplePanic</key>
			<false/>
			<key>DisableWatchDog</key>
			<false/>
			<key>DisplayDelay</key>
			<integer>0</integer>
			<key>DisplayLevel</key>
			<integer>2147483650</integer>
			<key>LogModules</key>
			<string>*</string>
			<key>SysReport</key>
			<false/>
			<key>Target</key>
			<integer>3</integer>
		</dict>
		<key>Entries</key>
		<array>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Auxiliary</key>
				<false/>
				<key>Comment</key>
				<string>Not signed for security reasons</string>
				<key>Enabled</key>
				<false/>
				<key>Flavour</key>
				<string>Auto</string>
				<key>Name</key>
				<string>CustomOS</string>
				<key>Path</key>
				<string>PciRoot(0x0)/Pci(0x1,0x1)/Pci(0x0,0x0)/NVMe(0x1,11-22-33-44-55-66-77-88)/HD(1,GPT,00000000-0000-0000-0000-000000000000,0x800,0x64000)/\EFI\BOOT\BOOTX64.EFI</string>
				<key>TextMode</key>
				<false/>
			</dict>
		</array>
		<key>Security</key>
		<dict>
			<key>AllowSetDefault</key>
			<false/>
			<key>ApECID</key>
			<integer>0</integer>
			<key>AuthRestart</key>
			<false/>
			<key>BlacklistAppleUpdate</key>
			<true/>
			<key>DmgLoading</key>
			<string>Signed</string>
			<key>EnablePassword</key>
			<false/>
			<key>ExposeSensitiveData</key>
			<integer>6</integer>
			<key>HaltLevel</key>
			<integer>2147483648</integer>
			<key>PasswordHash</key>
			<data></data>
			<key>PasswordSalt</key>
			<data></data>
			<key>ScanPolicy</key>
			<integer>17760515</integer>
			<key>SecureBootModel</key>
			<string>Default</string>
			<key>Vault</key>
			<string>Secure</string>
		</dict>
		<key>Serial</key>
		<dict>
			<key>Init</key>
			<false/>
			<key>Override</key>
			<false/>
		</dict>
		<key>Tools</key>
		<array>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Auxiliary</key>
				<true/>
				<key>Comment</key>
				<string>Not signed for security reasons</string>
				<key>Enabled</key>
				<false/>
				<key>Flavour</key>
				<string>OpenShell:UEFIShell:Shell</string>
				<key>FullNvramAccess</key>
				<false/>
				<key>Name</key>
				<string>UEFI Shell</string>
				<key>Path</key>
				<string>OpenShell.efi</string>
				<key>RealPath</key>
				<false/>
				<key>TextMode</key>
				<false/>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Auxiliary</key>
				<true/>
				<key>Comment</key>
				<string>Memory testing utility</string>
				<key>Enabled</key>
				<false/>
				<key>Flavour</key>
				<string>MemTest</string>
				<key>FullNvramAccess</key>
				<false/>
				<key>Name</key>
				<string>memtest86</string>
				<key>Path</key>
				<string>memtest86/BOOTX64.efi</string>
				<key>RealPath</key>
				<true/>
				<key>TextMode</key>
				<false/>
			</dict>
			<dict>
				<key>Arguments</key>
				<string>Shutdown</string>
				<key>Auxiliary</key>
				<true/>
				<key>Comment</key>
				<string>Perform shutdown</string>
				<key>Enabled</key>
				<false/>
				<key>Flavour</key>
				<string>Auto</string>
				<key>FullNvramAccess</key>
				<false/>
				<key>Name</key>
				<string>Shutdown</string>
				<key>Path</key>
				<string>ResetSystem.efi</string>
				<key>RealPath</key>
				<false/>
				<key>TextMode</key>
				<false/>
			</dict>
		</array>
	</dict>
	<key>NVRAM</key>
	<dict>
		<key>Add</key>
		<dict>
			<key>4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14</key>
			<dict>
				<key>DefaultBackgroundColor</key>
				<data>AAAAAA==</data>
			</dict>
			<key>4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102</key>
			<dict>
				<key>rtc-blacklist</key>
				<data></data>
			</dict>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<dict>
				<key>#INFO (prev-lang:kbd)</key>
				<string>en:252 (ABC), set 656e3a323532</string>
				<key>ForceDisplayRotationInEFI</key>
				<integer>0</integer>
				<key>SystemAudioVolume</key>
				<data>Rg==</data>
				<key>boot-args</key>
				<string>-v keepsyms=1</string>
				<key>csr-active-config</key>
				<data>AAAAAA==</data>
				<key>prev-lang:kbd</key>
				<data>cnUtUlU6MjUy</data>
				<key>run-efi-updater</key>
				<string>No</string>
			</dict>
		</dict>
		<key>Delete</key>
		<dict>
			<key>4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14</key>
			<array>
				<string>DefaultBackgroundColor</string>
			</array>
			<key>4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102</key>
			<array>
				<string>rtc-blacklist</string>
			</array>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<array>
				<string>boot-args</string>
				<string>ForceDisplayRotationInEFI</string>
			</array>
		</dict>
		<key>LegacyOverwrite</key>
		<false/>
		<key>LegacySchema</key>
		<dict>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<array>
				<string>EFILoginHiDPI</string>
				<string>EFIBluetoothDelay</string>
				<string>LocationServicesEnabled</string>
				<string>SystemAudioVolume</string>
				<string>SystemAudioVolumeDB</string>
				<string>SystemAudioVolumeSaved</string>
				<string>bluetoothActiveControllerInfo</string>
				<string>bluetoothInternalControllerInfo</string>
				<string>flagstate</string>
				<string>fmm-computer-name</string>
				<string>fmm-mobileme-token-FMM</string>
				<string>fmm-mobileme-token-FMM-BridgeHasAccount</string>
				<string>nvda_drv</string>
				<string>prev-lang:kbd</string>
				<string>backlight-level</string>
				<string>BootCampHD</string>
			</array>
			<key>8BE4DF61-93CA-11D2-AA0D-00E098032B8C</key>
			<array>
				<string>Boot0080</string>
				<string>Boot0081</string>
				<string>Boot0082</string>
				<string>BootNext</string>
				<string>BootOrder</string>
			</array>
		</dict>
		<key>WriteFlash</key>
		<true/>
	</dict>
	<key>PlatformInfo</key>
	<dict>
		<key>Automatic</key>
		<true/>
		<key>CustomMemory</key>
		<false/>
		<key>Generic</key>
		<dict>
			<key>AdviseFeatures</key>
			<false/>
			<key>MLB</key>
			<string>M0000000000000001</string>
			<key>MaxBIOSVersion</key>
			<false/>
			<key>ProcessorType</key>
			<integer>0</integer>
			<key>ROM</key>
			<data>ESIzRFVm</data>
			<key>SpoofVendor</key>
			<true/>
			<key>SystemMemoryStatus</key>
			<string>Auto</string>
			<key>SystemProductName</key>
			<string>iMac19,1</string>
			<key>SystemSerialNumber</key>
			<string>W00000000001</string>
			<key>SystemUUID</key>
			<string>00000000-0000-0000-0000-000000000000</string>
		</dict>
		<key>UpdateDataHub</key>
		<true/>
		<key>UpdateNVRAM</key>
		<true/>
		<key>UpdateSMBIOS</key>
		<true/>
		<key>UpdateSMBIOSMode</key>
		<string>Create</string>
		<key>UseRawUuidEncoding</key>
		<false/>
	</dict>
	<key>UEFI</key>
	<dict>
		<key>APFS</key>
		<dict>
			<key>EnableJumpstart</key>
			<true/>
			<key>GlobalConnect</key>
			<false/>
			<key>HideVerbose</key>
			<true/>
			<key>JumpstartHotPlug</key>
			<false/>
			<key>MinDate</key>
			<integer>0</integer>
			<key>MinVersion</key>
			<integer>0</integer>
		</dict>
		<key>AppleInput</key>
		<dict>
			<key>AppleEvent</key>
			<string>Builtin</string>
			<key>CustomDelays</key>
			<false/>
			<key>GraphicsInputMirroring</key>
			<true/>
			<key>KeyInitialDelay</key>
			<integer>50</integer>
			<key>KeySubsequentDelay</key>
			<integer>5</integer>
			<key>PointerDwellClickTimeout</key>
			<integer>0</integer>
			<key>PointerDwellDoubleClickTimeout</key>
			<integer>0</integer>
			<key>PointerDwellRadius</key>
			<integer>0</integer>
			<key>PointerPollMask</key>
			<integer>-1</integer>
			<key>PointerPollMax</key>
			<integer>80</integer>
			<key>PointerPollMin</key>
			<integer>10</integer>
			<key>PointerSpeedDiv</key>
			<integer>1</integer>
			<key>PointerSpeedMul</key>
			<integer>1</integer>
		</dict>
		<key>Audio</key>
		<dict>
			<key>AudioCodec</key>
			<integer>0</integer>
			<key>AudioDevice</key>
			<string>PciRoot(0x0)/Pci(0x1b,0x0)</string>
			<key>AudioOutMask</key>
			<integer>1</integer>
			<key>AudioSupport</key>
			<false/>
			<key>DisconnectHda</key>
			<false/>
			<key>MaximumGain</key>
			<integer>-15</integer>
			<key>MinimumAssistGain</key>
			<integer>-30</integer>
			<key>MinimumAudibleGain</key>
			<integer>-55</integer>
			<key>PlayChime</key>
			<string>Auto</string>
			<key>ResetTrafficClass</key>
			<false/>
			<key>SetupDelay</key>
			<integer>0</integer>
		</dict>
		<key>ConnectDrivers</key>
		<true/>
		<key>Drivers</key>
		<array>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<true/>
				<key>Path</key>
				<string>OpenVariableRuntimeDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenRuntime.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>VirtioPciDeviceDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Virtio10.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>VirtioSerialDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>VirtioScsiDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>VirtioBlkDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>VirtioGpuDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string>HFS+ Driver</string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>HfsPlus.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenCanopy.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>AudioDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenPartitionDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenUsbKbDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>UsbMouseDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Ps2KeyboardDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Ps2MouseDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>HiiDatabase.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>NvmExpressDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>XhciDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>ExFatDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>CrScreenshotDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Ext4Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>RngDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Hash2DxeCrypto.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>DpcDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>SnpDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>MnpDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>ArpDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Dhcp4Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Ip4Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Udp4Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Mtftp4Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Dhcp6Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Ip6Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Udp6Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>Mtftp6Dxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>TcpDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>UefiPxeBcDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>DnsDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>HttpDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>HttpUtilitiesDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>HttpBootDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>TlsDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>RamDiskDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>VirtioNetDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenLinuxBoot.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenNetworkBoot.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>ResetNvramEntry.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>ToggleSipEntry.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<false/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>FirmwareSettingsEntry.efi</string>
			</dict>
		</array>
		<key>Input</key>
		<dict>
			<key>KeyFiltering</key>
			<false/>
			<key>KeyForgetThreshold</key>
			<integer>5</integer>
			<key>KeySupport</key>
			<true/>
			<key>KeySupportMode</key>
			<string>Auto</string>
			<key>KeySwap</key>
			<false/>
			<key>PointerSupport</key>
			<false/>
			<key>PointerSupportMode</key>
			<string>ASUS</string>
			<key>TimerResolution</key>
			<integer>50000</integer>
		</dict>
		<key>Output</key>
		<dict>
			<key>ClearScreenOnModeSwitch</key>
			<false/>
			<key>ConsoleFont</key>
			<string></string>
			<key>ConsoleMode</key>
			<string></string>
			<key>DirectGopRendering</key>
			<false/>
			<key>ForceResolution</key>
			<false/>
			<key>GopBurstMode</key>
			<false/>
			<key>GopPassThrough</key>
			<string>Disabled</string>
			<key>IgnoreTextInGraphics</key>
			<false/>
			<key>InitialMode</key>
			<string>Auto</string>
			<key>ProvideConsoleGop</key>
			<true/>
			<key>ReconnectGraphicsOnConnect</key>
			<false/>
			<key>ReconnectOnResChange</key>
			<false/>
			<key>ReplaceTabWithSpace</key>
			<false/>
			<key>Resolution</key>
			<string>Max</string>
			<key>SanitiseClearScreen</key>
			<false/>
			<key>TextRenderer</key>
			<string>BuiltinGraphics</string>
			<key>UIScale</key>
			<integer>0</integer>
			<key>UgaPassThrough</key>
			<false/>
		</dict>
		<key>ProtocolOverrides</key>
		<dict>
			<key>AppleAudio</key>
			<false/>
			<key>AppleBootPolicy</key>
			<false/>
			<key>AppleDebugLog</key>
			<false/>
			<key>AppleEg2Info</key>
			<false/>
			<key>AppleFramebufferInfo</key>
			<false/>
			<key>AppleImageConversion</key>
			<false/>
			<key>AppleImg4Verification</key>
			<false/>
			<key>AppleKeyMap</key>
			<false/>
			<key>AppleRtcRam</key>
			<false/>
			<key>AppleSecureBoot</key>
			<false/>
			<key>AppleSmcIo</key>
			<false/>
			<key>AppleUserInterfaceTheme</key>
			<false/>
			<key>DataHub</key>
			<false/>
			<key>DeviceProperties</key>
			<false/>
			<key>FirmwareVolume</key>
			<true/>
			<key>HashServices</key>
			<false/>
			<key>OSInfo</key>
			<false/>
			<key>PciIo</key>
			<false/>
			<key>UnicodeCollation</key>
			<false/>
		</dict>
		<key>Quirks</key>
		<dict>
			<key>ActivateHpetSupport</key>
			<false/>
			<key>DisableSecurityPolicy</key>
			<false/>
			<key>EnableVectorAcceleration</key>
			<true/>
			<key>EnableVmx</key>
			<false/>
			<key>ExitBootServicesDelay</key>
			<integer>0</integer>
			<key>ForceOcWriteFlash</key>
			<false/>
			<key>ForgeUefiSupport</key>
			<false/>
			<key>IgnoreInvalidFlexRatio</key>
			<false/>
			<key>ReleaseUsbOwnership</key>
			<false/>
			<key>ReloadOptionRoms</key>
			<false/>
			<key>RequestBootVarRouting</key>
			<true/>
			<key>ResizeGpuBars</key>
			<integer>-1</integer>
			<key>ResizeUsePciRbIo</key>
			<false/>
			<key>ShimRetainProtocol</key>
			<false/>
			<key>TscSyncTimeout</key>
			<integer>0</integer>
			<key>UnblockFsConnect</key>
			<false/>
		</dict>
		<key>ReservedMemory</key>
		<array>
			<dict>
				<key>Address</key>
				<integer>*********</integer>
				<key>Comment</key>
				<string>HD3000: IGPU memory corruption errata</string>
				<key>Enabled</key>
				<false/>
				<key>Size</key>
				<integer>*********</integer>
				<key>Type</key>
				<string>Reserved</string>
			</dict>
			<dict>
				<key>Address</key>
				<integer>569344</integer>
				<key>Comment</key>
				<string>Fix black screen on wake from hibernation for Lenovo Thinkpad T490</string>
				<key>Enabled</key>
				<false/>
				<key>Size</key>
				<integer>4096</integer>
				<key>Type</key>
				<string>RuntimeCode</string>
			</dict>
		</array>
		<key>Unload</key>
		<array/>
	</dict>
</dict>
</plist>
